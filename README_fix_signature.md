# PDF手写签名功能修复指南

本文档提供了修复PDF手写签名功能的详细步骤和说明。

## 问题描述

系统在使用PDF手写签名功能时出现以下错误：

1. `获取用户已保存签名时出错: type object 'UserSignature' has no attribute 'signature_id'`
2. `访问PDF手写签名页面失败: 'File' object has no attribute 'physical_path'`
3. `获取用户已保存签名时出错: type object 'Signature' has no attribute 'signature_type'`

这些错误是由于数据库模型缺少必要字段导致的：
- `UserSignature`模型中缺少`signature_id`字段
- `File`模型中缺少`physical_path`字段
- `Signature`模型中缺少`signature_type`和`signature_position`字段

## 修复方法

### Windows环境下修复

1. 使用修复脚本更新数据库模型：

```bash
# 修复数据库模型
python fix_db_models.py
```

2. 如果使用MySQL数据库，执行MySQL专用修复脚本：

```bash
python fix_db_mysql.py
```

### Ubuntu环境下修复

1. 给修复脚本执行权限：

```bash
chmod +x fix_signature_ubuntu.sh
```

2. 运行修复脚本：

```bash
./fix_signature_ubuntu.sh
```

该脚本会自动执行以下操作：
- 修复数据库模型
- 为数据表添加缺少的字段
- 重启应用服务

## 验证修复

修复完成后，可以通过以下步骤验证：

1. 打开任意PDF文件预览页面
2. 点击"手写签名"按钮
3. 确认能够正常进入签名页面并看到之前保存的签名记录
4. 在PDF上添加签名并保存
5. 验证签名是否成功保存并显示

## 技术说明

本次修复主要涉及三个数据库模型的更新：

1. `UserSignature`模型增加`signature_id`字段，关联到`Signature`表
2. `File`模型增加`physical_path`字段，用于存储文件的绝对路径
3. `Signature`模型增加`signature_type`和`signature_position`字段，用于记录签名类型和位置

修复后的模型定义如下：

```python
# UserSignature模型
class UserSignature(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    signature_id = db.Column(db.Integer, db.ForeignKey('signature.id'), nullable=True)
    signature_data = db.Column(db.Text, nullable=False)  # 签名图像数据(Base64)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    is_default = db.Column(db.Boolean, default=True)  # 是否为默认签名
    description = db.Column(db.Text)  # 签名描述

# File模型
class File(db.Model):
    # ... 其他字段 ...
    path = db.Column(db.String(500), default='')
    physical_path = db.Column(db.String(255), nullable=True)
    # ... 其他字段 ...

# Signature模型
class Signature(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    signature_data = db.Column(db.Text, nullable=False)  # 签名图像数据(Base64)
    signature_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    signature_metadata = db.Column(db.Text)  # 存储签名时的元数据(JSON格式)
    signature_type = db.Column(db.String(50), default='handwriting')  # 签名类型：handwriting, digital等
    signature_position = db.Column(db.String(100))  # 签名位置信息
```

## 注意事项

1. 修复仅添加缺少的字段，不会影响现有数据
2. 如果修复后仍有问题，请检查日志文件获取详细错误信息
3. 在Ubuntu环境部署前，请确保已执行Windows环境下的修复脚本，然后再在Ubuntu环境中执行fix_signature_ubuntu.sh

## 常见问题

### 仍然无法点击PDF添加签名

检查浏览器控制台是否有JavaScript错误，确保：
- 所有必要的JavaScript库已正确加载
- 签名层的z-index值大于iframe的z-index值
- 签名层的pointer-events设置为auto

### 签名无法保存

检查服务器日志中是否有错误信息，确保：
- 服务器有足够的磁盘空间
- 应用程序有权限写入文件
- 数据库连接正常

### 移动设备上无法使用

确保添加了触摸事件支持，并且正确处理了触摸事件。

## 联系支持

如有任何问题，请联系技术支持团队。 