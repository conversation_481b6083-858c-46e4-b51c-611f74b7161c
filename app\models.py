from datetime import datetime
from app import db, login_manager
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import pytz

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 用户-组关联表
user_group_members = db.Table('user_group_members',
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>('user.id'), primary_key=True),
    db.<PERSON>umn('group_id', db.Integer, db.<PERSON>('user_group.id'), primary_key=True),
    db.Column('join_date', db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
)

class UserGroup(db.Model):
    """用户组模型，用于管理用户分组和权限"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(255))
    created_by = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('user.id'), nullable=False)
    create_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    
    # 权限设置
    can_upload = db.Column(db.Boolean, default=True)  # 是否可以上传文件
    can_download = db.Column(db.Boolean, default=True)  # 是否可以下载文件
    can_delete = db.Column(db.Boolean, default=True)  # 是否可以删除文件
    can_share = db.Column(db.Boolean, default=True)  # 是否可以共享文件
    storage_limit = db.Column(db.Integer, default=0)  # 存储限制(MB)，0表示无限制
    is_admin_group = db.Column(db.Boolean, default=False)  # 是否为管理员组
    
    # 关系
    creator = db.relationship('User', backref='created_groups', foreign_keys=[created_by])
    members = db.relationship('User', secondary=user_group_members, 
                              backref=db.backref('groups', lazy='dynamic'))

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.Boolean, default=False)
    files = db.relationship('File', backref='owner', lazy=True)
    folders = db.relationship('Folder', backref='owner', lazy=True)
    security_question = db.Column(db.String(255))  # 安全问题
    security_answer_hash = db.Column(db.String(128))  # 安全问题答案哈希
    default_signatures = db.relationship('UserSignature', backref='user', lazy='dynamic')

    @property
    def is_super_admin(self):
        """检查用户是否是超级管理员"""
        # 用户名为'cv24051'的用户是超级管理员
        if self.username == 'cv24051':
            return True
        # 如果用户有is_superuser字段并且为True，则是超级管理员
        if hasattr(self, 'is_superuser') and self.is_superuser:
            return True
        return False

    @property
    def is_authenticated(self):
        return True

    @property
    def is_active(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def set_security_question(self, question, answer):
        """设置安全问题和答案"""
        self.security_question = question
        self.security_answer_hash = generate_password_hash(answer.lower().strip())
        
    def check_security_answer(self, answer):
        """验证安全问题答案"""
        if not self.security_answer_hash:
            return False
        return check_password_hash(self.security_answer_hash, answer.lower().strip())

    def has_permission(self, permission_type):
        """检查用户是否拥有特定权限
        permission_type: 'upload', 'download', 'delete', 'share'
        """
        # 管理员拥有所有权限
        if self.is_admin:
            return True
            
        # 检查用户所在组的权限
        for group in self.groups:
            if permission_type == 'upload' and group.can_upload:
                return True
            elif permission_type == 'download' and group.can_download:
                return True
            elif permission_type == 'delete' and group.can_delete:
                return True
            elif permission_type == 'share' and group.can_share:
                return True
                
        # 如果用户没有组或组没有该权限，默认允许基本操作
        if permission_type in ['upload', 'download']:
            return True
        return False
    
    def get_storage_limit(self):
        """获取用户的存储限制(MB)"""
        # 管理员无限制
        if self.is_admin:
            return 0  # 0表示无限制
            
        # 获取用户所在组中最大的存储限制
        max_limit = 0
        for group in self.groups:
            if group.storage_limit > max_limit or group.storage_limit == 0:
                if group.storage_limit == 0:  # 如果任何组是无限制的
                    return 0
                max_limit = group.storage_limit
                
        # 如果没有设置限制，返回默认值
        return max_limit if max_limit > 0 else 100  # 默认100MB

    def is_in_admin_group(self):
        """检查用户是否在管理员组中"""
        for group in self.groups:
            if group.is_admin_group:
                return True
        return False

class Keyword(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    word = db.Column(db.String(50), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    create_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    folder_id = db.Column(db.Integer, db.ForeignKey('folder.id'), nullable=True)  # 关联的文件夹，为空表示全局关键字
    keyword_type = db.Column(db.Integer, default=0)  # 0=通用, 1=关键字1, 2=关键字2
    
    user = db.relationship('User', backref=db.backref('keywords', lazy='dynamic'))
    folder = db.relationship('Folder', backref=db.backref('keywords', lazy='dynamic'))

class Folder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    path = db.Column(db.String(500), default='')
    parent_id = db.Column(db.Integer, db.ForeignKey('folder.id'), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    create_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    tags = db.Column(db.String(500))
    
    # 添加权限控制字段
    is_public = db.Column(db.Boolean, default=False)  # 是否公开访问
    allowed_users = db.Column(db.String(500), default='')  # 允许访问的用户ID列表,逗号分隔
    allowed_groups = db.Column(db.String(500), default='')  # 允许访问的用户组ID列表,逗号分隔
    read_only = db.Column(db.Boolean, default=False)  # 是否只读
    
    # 关系
    parent = db.relationship('Folder', remote_side=[id], backref='subfolders')
    files = db.relationship('File', backref='folder', lazy=True)

    is_deleted = db.Column(db.Boolean, default=False)  # 是否在回收站
    delete_time = db.Column(db.DateTime)  # 删除时间

    def can_access(self, user):
        """检查用户是否有权限访问此文件夹"""
        # 文件夹所有者可以访问
        if self.user_id == user.id:
            return True
        
        # 超级管理员和cv24051用户可以访问
        if user.is_super_admin:
            return True
            
        # 公开文件夹可以访问
        if self.is_public:
            return True
            
        # 检查用户是否在允许名单中
        if self.allowed_users:
            allowed_user_ids = [int(id.strip()) for id in self.allowed_users.split(',') if id.strip()]
            if user.id in allowed_user_ids:
                return True
                
        # 检查用户是否在允许的用户组中
        if self.allowed_groups:
            allowed_group_ids = [int(id.strip()) for id in self.allowed_groups.split(',') if id.strip()]
            
            # 直接使用User-Group多对多关系查询用户所在的组
            user_groups = user.groups.all()
            user_group_ids = [group.id for group in user_groups]
            
            # 如果用户所在的任何组在允许名单中，则允许访问
            if any(group_id in allowed_group_ids for group_id in user_group_ids):
                return True
                
        return False
        
    def can_modify(self, user):
        """检查用户是否有权限修改此文件夹内容"""
        # 文件夹所有者可以修改
        if self.user_id == user.id:
            return True
            
        # 超级管理员和cv24051用户可以修改
        if user.is_super_admin:
            return True
            
        # 如果文件夹是只读的，即使用户有访问权限也不能修改
        if self.read_only:
            return False
            
        # 检查用户是否在允许名单中
        if self.allowed_users:
            allowed_user_ids = [int(id.strip()) for id in self.allowed_users.split(',') if id.strip()]
            if user.id in allowed_user_ids:
                return True
                
        # 检查用户是否在允许的用户组中
        if self.allowed_groups:
            allowed_group_ids = [int(id.strip()) for id in self.allowed_groups.split(',') if id.strip()]
            
            # 直接使用User-Group多对多关系查询用户所在的组
            user_groups = user.groups.all()
            user_group_ids = [group.id for group in user_groups]
            
            # 如果用户所在的任何组在允许名单中，则允许修改
            if any(group_id in allowed_group_ids for group_id in user_group_ids):
                return True
                
        return False

    def get_accessible_users(self):
        """获取所有可以访问此文件夹的用户ID列表"""
        users = set()
        
        # 添加所有者
        users.add(str(self.user_id))
        
        # 添加授权用户
        if self.allowed_users:
            users.update(id.strip() for id in self.allowed_users.split(',') if id.strip())
            
        return list(users)

class File(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)
    upload_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    tags = db.Column(db.String(500))
    path = db.Column(db.String(500), default='')
    physical_path = db.Column(db.String(255), nullable=True)
    folder_id = db.Column(db.Integer, db.ForeignKey('folder.id'), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_deleted = db.Column(db.Boolean, default=False)  # 是否在回收站
    delete_time = db.Column(db.DateTime)  # 删除时间

    def can_access(self, user):
        """检查用户是否有权限访问此文件"""
        # 文件所有者可以访问
        if self.user_id == user.id:
            return True
            
        # 超级管理员和cv24051用户可以访问
        if user.is_super_admin:
            return True
            
        # 检查文件所在文件夹的权限
        if self.folder_id:
            folder = Folder.query.get(self.folder_id)
            if folder and folder.can_access(user):
                return True
        
        return False
        
    def can_modify(self, user):
        """检查用户是否有权限修改此文件"""
        # 文件所有者可以修改
        if self.user_id == user.id:
            return True
            
        # 超级管理员和cv24051用户可以修改
        if user.is_super_admin:
            return True
            
        # 检查文件所在文件夹的权限
        if self.folder_id:
            folder = Folder.query.get(self.folder_id)
            if folder and folder.can_modify(user):
                return True
                
        return False

class UserLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    action = db.Column(db.String(50))  # 操作类型
    details = db.Column(db.Text)       # 操作详情
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    ip_address = db.Column(db.String(50))  # 操作IP
    target_id = db.Column(db.Integer)      # 操作对象ID（文件或文件夹ID）
    target_type = db.Column(db.String(20)) # 操作对象类型（file, folder, user等）
    
    user = db.relationship('User', backref=db.backref('logs', lazy='dynamic')) 

# 电子签名模型
class Signature(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    file_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    signature_data = db.Column(db.Text, nullable=False)  # 签名图像数据(Base64)
    signature_date = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    signature_metadata = db.Column(db.Text)  # 存储签名时的元数据(JSON格式)
    signature_type = db.Column(db.String(50), default='handwriting')  # 签名类型：handwriting, digital等
    signature_position = db.Column(db.String(100))  # 签名位置信息
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('signatures', lazy='dynamic'))
    file = db.relationship('File', backref=db.backref('signatures', lazy='dynamic'))

# 用户默认电子签名模型
class UserSignature(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    signature_id = db.Column(db.Integer, db.ForeignKey('signature.id'), nullable=True)
    signature_data = db.Column(db.Text, nullable=False)  # 签名图像数据(Base64)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    is_default = db.Column(db.Boolean, default=True)  # 是否为默认签名
    description = db.Column(db.Text)  # 签名描述 