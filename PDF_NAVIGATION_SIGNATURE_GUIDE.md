# PDF导航与手写签名功能使用指南

## 🎯 功能概述

PDF手写签名界面现在支持完整的文档导航功能，让您可以：

✅ **自由移动PDF文档** - 滚动查看文档的不同部分  
✅ **缩放PDF文档** - 放大缩小以获得最佳查看效果  
✅ **精确放置签名** - 在任意位置添加手写签名  
✅ **签名跟随文档** - 签名位置相对于文档固定，不受缩放影响  

## 🖱️ PDF导航控制

### 缩放控制
在PDF容器右上角有缩放控制按钮：

- **🔍➕ 放大** - 放大PDF文档
- **🔍➖ 缩小** - 缩小PDF文档  
- **🔄 重置** - 恢复100%缩放并回到顶部
- **↔️ 适应宽度** - 自动调整到适合容器宽度

### 快捷键操作
- **Ctrl + 滚轮** - 缩放文档
- **Ctrl + Plus(+)** - 放大
- **Ctrl + Minus(-)** - 缩小
- **Ctrl + 0** - 重置到100%

### 滚动导航
- **鼠标滚轮** - 上下滚动文档
- **拖拽滚动条** - 快速导航到文档任意位置
- **键盘方向键** - 精确滚动

## ✍️ 手写签名功能

### 添加新签名
1. **点击"新建签名"按钮**
   - 鼠标变为十字形
   - 状态提示显示"请点击PDF文档上您想要添加签名的位置"

2. **导航到目标位置**
   - 使用缩放控制放大到合适大小
   - 滚动到需要签名的文档位置

3. **点击放置签名**
   - 点击PDF文档上的精确位置
   - 系统会自动计算相对于文档的位置（考虑缩放和滚动）

4. **绘制签名**
   - 在弹出的签名板上用鼠标绘制签名
   - 可调整笔迹颜色和粗细
   - 可选择保存为模板

5. **确认签名**
   - 点击"确认签名"完成添加

### 使用已保存签名
1. **选择签名模板**
   - 在下拉框中选择已保存的签名

2. **导航并放置**
   - 导航到目标位置
   - 点击PDF文档放置签名

### 管理现有签名

每个签名都有控制按钮：

- **🖊️ 修改**（黄色）- 重新绘制签名内容
- **➕ 放大**（绿色）- 增大签名尺寸
- **➖ 缩小**（蓝色）- 减小签名尺寸
- **🗑️ 删除**（红色）- 删除签名

### 拖拽移动签名
- **直接拖拽** - 用鼠标拖动签名到新位置
- **智能限制** - 签名会限制在文档范围内
- **缩放适应** - 拖拽时自动考虑当前缩放级别

## 🔧 技术特性

### 位置计算
签名位置使用相对坐标系统：
- 位置相对于原始文档尺寸计算
- 不受缩放级别影响
- 考虑滚动偏移量
- 确保签名始终在正确位置

### 缩放支持
- **缩放范围**: 50% - 300%
- **缩放步长**: 10%
- **平滑缩放**: CSS transform实现
- **位置保持**: 签名位置自动调整

### 滚动支持
- **无限滚动**: 支持大型PDF文档
- **位置跟踪**: 实时计算滚动偏移
- **边界检测**: 防止签名超出文档范围

## 📋 使用技巧

### 最佳实践
1. **先导航后签名** - 先找到目标位置，再添加签名
2. **适当缩放** - 放大到150%-200%获得最佳精度
3. **使用模板** - 保存常用签名提高效率
4. **检查位置** - 添加签名后可以缩放查看效果

### 工作流程建议
1. **文档预览** - 先浏览整个文档了解结构
2. **标记位置** - 确定需要签名的位置
3. **逐一签名** - 按顺序添加所有签名
4. **最终检查** - 缩放到100%检查整体效果

## 🐛 故障排除

### 常见问题

**Q: 签名位置不准确？**
A: 确保在添加签名前已经停止滚动，等待页面稳定后再点击

**Q: 缩放后签名变模糊？**
A: 这是正常现象，签名图像会随缩放调整，保存时使用原始清晰度

**Q: 拖拽签名时卡顿？**
A: 在较大缩放级别下可能出现，建议在100%缩放下进行拖拽操作

**Q: 签名超出文档边界？**
A: 系统会自动限制签名在文档范围内，如果发生请刷新页面

### 性能优化建议
- 避免在300%缩放下进行复杂操作
- 大型PDF文档建议分段处理
- 定期保存签名避免数据丢失

## 🎨 界面说明

### 状态指示器
- **缩放百分比** - 左上角显示当前缩放级别
- **状态消息** - 工具栏中显示当前操作提示
- **鼠标样式** - 十字形表示等待点击位置

### 视觉反馈
- **签名高亮** - 鼠标悬停时签名边框高亮
- **控制按钮** - 悬停时显示操作按钮
- **拖拽提示** - 拖拽时签名置于最前层

## 🚀 高级功能

### 键盘快捷键
- **Tab** - 在签名间切换焦点
- **Delete** - 删除选中的签名
- **Escape** - 取消当前操作

### 批量操作
- 可以连续添加多个签名
- 支持批量调整签名大小
- 一次性删除多个签名

### 导出功能
- 签名位置信息保存到数据库
- 支持导出带签名的PDF
- 保持签名的精确位置

---

## 📞 技术支持

如果遇到问题：
1. 检查浏览器控制台是否有错误信息
2. 确保浏览器支持HTML5 Canvas
3. 刷新页面重试
4. 联系技术支持并提供详细错误描述

**享受您的PDF手写签名体验！** 🎉
