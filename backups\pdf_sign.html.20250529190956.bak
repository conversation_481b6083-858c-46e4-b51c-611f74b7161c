{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ filename }} - 添加手写签名</h5>
                    <div class="btn-group">
                        <button id="btnSaveSignature" class="btn btn-sm btn-success">
                            <i class="fas fa-save"></i> 保存签名
                        </button>
                        <button id="btnClearSignature" class="btn btn-sm btn-warning">
                            <i class="fas fa-eraser"></i> 清除签名
                        </button>
                        <a href="{{ url_for('main.preview_file', file_id=file_id) }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <!-- 签名工具栏 -->
                    <div class="bg-light p-2 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹颜色</span>
                                    <input type="color" id="penColor" class="form-control form-control-color" value="#000000">
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹粗细</span>
                                    <input type="range" id="penSize" class="form-range" min="1" max="10" value="2" style="width: 100px;">
                                    <span class="input-group-text" id="penSizeValue">2px</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">使用保存的签名</span>
                                    <select id="selectSignature" class="form-select form-select-sm">
                                        <option value="">-- 选择签名 --</option>
                                        {% for signature in saved_signatures %}
                                        <option value="{{ signature.id }}" data-signature="{{ signature.data }}">{{ signature.date }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto">
                                <span class="text-muted small">点击PDF文档位置添加签名</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- PDF容器 -->
                    <div id="pdfContainer" class="pdf-container position-relative" style="min-height: 800px;">
                        <!-- iframe用于显示PDF文件 -->
                        <iframe id="pdfViewer" src="{{ file_url }}" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; border: none;"></iframe>
                        
                        <!-- 签名层 -->
                        <div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="pointer-events: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 签名模态框 -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加签名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3 text-center">
                    <p>请在下方框内签署您的签名</p>
                    <div class="signature-pad-container border rounded" style="background-color: white;">
                        <canvas id="signaturePad" width="450" height="200" style="width: 100%; height: 200px;"></canvas>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearPad">清除</button>
                <button type="button" class="btn btn-primary" id="confirmSignature">确认签名</button>
            </div>
        </div>
    </div>
</div>

<style>
    .pdf-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        overflow: auto;
        height: calc(100vh - 200px);
    }
    
    .signature-item {
        position: absolute;
        cursor: move;
        z-index: 100;
    }
    
    .signature-image {
        max-width: 200px;
        border: 1px dashed transparent;
    }
    
    .signature-item:hover .signature-image {
        border-color: #007bff;
    }
    
    .signature-controls {
        display: none;
    }
    
    .signature-item:hover .signature-controls {
        display: block;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("PDF签名页面初始化...");
    
    // 初始化变量
    const signatureLayer = document.getElementById('signatureLayer');
    const pdfContainer = document.getElementById('pdfContainer');
    const btnSaveSignature = document.getElementById('btnSaveSignature');
    const btnClearSignature = document.getElementById('btnClearSignature');
    const penColor = document.getElementById('penColor');
    const penSize = document.getElementById('penSize');
    const penSizeValue = document.getElementById('penSizeValue');
    const selectSignature = document.getElementById('selectSignature');
    
    // 确保模态框元素存在
    if (!document.getElementById('signatureModal')) {
        console.error("找不到签名模态框元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    const signatureModal = new bootstrap.Modal(document.getElementById('signatureModal'));
    
    let signatureItems = [];
    let currentSignature = null;
    let isDragging = false;
    let offsetX, offsetY;
    
    // 初始化签名板
    const canvas = document.getElementById('signaturePad');
    if (!canvas) {
        console.error("找不到签名板元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    const signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)',
        minWidth: 1,
        maxWidth: 4
    });
    
    console.log("签名板初始化完成");
    
    // 自适应调整画布大小
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear(); // 调整大小后清除
    }
    
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    
    // 点击PDF区域时，显示签名模态框
    if (pdfContainer) {
        console.log("为PDF容器添加点击事件");
        pdfContainer.addEventListener('click', function(e) {
            console.log("PDF容器被点击", e.target);
            
            // 忽略已有签名上的点击
            if (e.target.closest('.signature-item')) {
                console.log("点击了现有签名，忽略");
                return;
            }
            
            // 计算点击位置
            const rect = pdfContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            console.log(`点击位置: x=${x}, y=${y}`);
            
            // 暂存位置信息
            currentSignature = { x, y };
            
            // 显示签名板
            signatureModal.show();
        });
    } else {
        console.error("找不到PDF容器元素！");
    }
    
    // 清除签名板
    const clearPadBtn = document.getElementById('clearPad');
    if (clearPadBtn) {
        clearPadBtn.addEventListener('click', function() {
            console.log("清除签名板");
            signaturePad.clear();
        });
    }
    
    // 确认签名按钮点击事件
    const confirmBtn = document.getElementById('confirmSignature');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            console.log("确认签名按钮被点击");
            if (signaturePad.isEmpty()) {
                alert('请先签名');
                return;
            }
            
            const signatureData = signaturePad.toDataURL();
            console.log("获取签名数据成功，长度:", signatureData.length);
            addSignatureToDocument(signatureData, currentSignature.x, currentSignature.y);
            signatureModal.hide();
        });
    }
    
    // 如果有预保存的签名，添加选择事件
    if (selectSignature) {
        selectSignature.addEventListener('change', function() {
            console.log("选择了保存的签名");
            const selectedOption = this.options[this.selectedIndex];
            if (!selectedOption.value) return;
            
            // 获取选中的签名数据
            const signatureData = selectedOption.getAttribute('data-signature');
            
            // 询问用户放置位置
            alert('请点击PDF上您希望放置签名的位置');
            
            // 监听一次性点击事件
            const clickHandler = function(e) {
                const rect = pdfContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                console.log(`选择放置位置: x=${x}, y=${y}`);
                addSignatureToDocument(signatureData, x, y);
                
                // 移除事件监听器
                pdfContainer.removeEventListener('click', clickHandler);
                
                // 重置选择框
                selectSignature.selectedIndex = 0;
            };
            
            // 添加一次性点击事件
            pdfContainer.addEventListener('click', clickHandler);
        });
    }
    
    // 添加签名到文档
    function addSignatureToDocument(signatureData, posX, posY) {
        console.log(`添加签名到文档: x=${posX}, y=${posY}`);
        // 创建签名元素
        const signatureItem = document.createElement('div');
        signatureItem.className = 'signature-item';
        signatureItem.style.left = posX + 'px';
        signatureItem.style.top = posY + 'px';
        signatureItem.style.position = 'absolute';
        
        // 创建签名图像
        const signatureImg = document.createElement('img');
        signatureImg.src = signatureData;
        signatureImg.className = 'signature-image';
        signatureImg.alt = '签名';
        signatureImg.draggable = false;
        signatureImg.style.maxHeight = '100px';
        signatureItem.appendChild(signatureImg);
        
        // 创建控制按钮
        const controls = document.createElement('div');
        controls.className = 'signature-controls';
        controls.style.position = 'absolute';
        controls.style.top = '-20px';
        controls.style.right = '0';
        
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn btn-sm btn-danger';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.addEventListener('click', function() {
            console.log("删除签名");
            signatureLayer.removeChild(signatureItem);
            // 从数组中移除
            const index = signatureItems.findIndex(item => item.element === signatureItem);
            if (index !== -1) {
                signatureItems.splice(index, 1);
            }
        });
        controls.appendChild(deleteBtn);
        signatureItem.appendChild(controls);
        
        // 添加拖拽功能
        signatureItem.addEventListener('mousedown', function(e) {
            // 忽略控制按钮上的点击
            if (e.target.closest('.signature-controls')) {
                return;
            }
            
            console.log("开始拖动签名");
            isDragging = true;
            currentSignature = signatureItem;
            
            const rect = signatureItem.getBoundingClientRect();
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;
            
            // 确保签名元素在拖动时显示在最前面
            signatureItem.style.zIndex = 1000;
            
            e.preventDefault();
        });
        
        // 添加到签名层
        signatureLayer.appendChild(signatureItem);
        signatureItems.push({
            element: signatureItem,
            data: signatureData,
            x: posX,
            y: posY
        });
        
        // 启用鼠标事件
        signatureItem.style.pointerEvents = 'auto';
        console.log("签名元素已添加到文档");
    }
    
    // 处理拖拽
    document.addEventListener('mousemove', function(e) {
        if (!isDragging || !currentSignature) return;
        
        const rect = pdfContainer.getBoundingClientRect();
        const x = e.clientX - rect.left - offsetX;
        const y = e.clientY - rect.top - offsetY;
        
        // 限制在PDF容器内
        const maxX = rect.width - currentSignature.offsetWidth;
        const maxY = rect.height - currentSignature.offsetHeight;
        const boundedX = Math.max(0, Math.min(x, maxX));
        const boundedY = Math.max(0, Math.min(y, maxY));
        
        currentSignature.style.left = boundedX + 'px';
        currentSignature.style.top = boundedY + 'px';
        
        // 更新数组中的位置
        const index = signatureItems.findIndex(item => item.element === currentSignature);
        if (index !== -1) {
            signatureItems[index].x = boundedX;
            signatureItems[index].y = boundedY;
        }
    });
    
    document.addEventListener('mouseup', function() {
        if (isDragging) {
            console.log("结束拖动签名");
            // 恢复正常的z-index
            if (currentSignature) {
                currentSignature.style.zIndex = '';
            }
        }
        isDragging = false;
        currentSignature = null;
    });
    
    // 笔迹粗细变化
    if (penSize && penSizeValue) {
        penSize.addEventListener('input', function() {
            const size = this.value;
            penSizeValue.textContent = size + 'px';
            signaturePad.minWidth = size / 2;
            signaturePad.maxWidth = size * 2;
        });
    }
    
    // 笔迹颜色变化
    if (penColor) {
        penColor.addEventListener('input', function() {
            signaturePad.penColor = this.value;
        });
    }
    
    // 清除所有签名
    if (btnClearSignature) {
        btnClearSignature.addEventListener('click', function() {
            console.log("清除所有签名");
            if (confirm('确定要清除所有签名吗？')) {
                while (signatureLayer.firstChild) {
                    signatureLayer.removeChild(signatureLayer.firstChild);
                }
                signatureItems = [];
            }
        });
    }
    
    // 保存签名
    if (btnSaveSignature) {
        btnSaveSignature.addEventListener('click', function() {
            console.log("保存签名按钮被点击");
            if (signatureItems.length === 0) {
                alert('请先添加至少一个签名');
                return;
            }
            
            // 显示加载提示
            const loadingToast = document.createElement('div');
            loadingToast.className = 'position-fixed top-50 start-50 translate-middle p-3 bg-dark text-white rounded';
            loadingToast.style.zIndex = 9999;
            loadingToast.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div> 正在保存签名...';
            document.body.appendChild(loadingToast);
            
            // 使用html2canvas捕获整个PDF和签名
            setTimeout(() => {
                console.log("开始捕获PDF和签名");
                html2canvas(pdfContainer, {
                    allowTaint: true,
                    useCORS: true,
                    logging: true, // 启用日志
                    onclone: function(clonedDoc) {
                        console.log("文档已克隆");
                        // 可以在这里修改克隆的文档
                    }
                }).then(canvas => {
                    console.log("PDF和签名捕获成功");
                    const screenshot = canvas.toDataURL('image/png');
                    console.log("截图数据长度:", screenshot.length);
                    
                    // 准备签名数据
                    const signatures = signatureItems.map(item => ({
                        data: item.data,
                        x: item.x,
                        y: item.y
                    }));
                    
                    console.log("准备发送签名数据到服务器");
                    // 发送到服务器
                    fetch('{{ url_for("main.save_pdf_signature", file_id=file_id) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            screenshot: screenshot,
                            signatures: signatures
                        })
                    })
                    .then(response => {
                        console.log("服务器响应状态:", response.status);
                        return response.json();
                    })
                    .then(data => {
                        document.body.removeChild(loadingToast);
                        console.log("服务器响应数据:", data);
                        if (data.success) {
                            alert('签名保存成功!');
                            window.location.href = '{{ url_for("main.preview_file", file_id=file_id) }}';
                        } else {
                            alert('保存失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        document.body.removeChild(loadingToast);
                        console.error('Error:', error);
                        alert('保存签名时出错: ' + error.message);
                    });
                }).catch(error => {
                    document.body.removeChild(loadingToast);
                    console.error('截图错误:', error);
                    alert('创建签名截图时出错: ' + error.message);
                });
            }, 500); // 增加延迟，确保UI渲染完成
        });
    }

    // 确保iframe内容加载完成后再绑定事件
    const pdfViewer = document.getElementById('pdfViewer');
    if (pdfViewer) {
        pdfViewer.onload = function() {
            console.log("PDF iframe已加载完成");
            // 重新绑定PDF容器点击事件（因为iframe加载可能会影响事件传播）
            setTimeout(function() {
                console.log("重新激活PDF点击事件");
                // 强制使signatureLayer可点击
                signatureLayer.style.pointerEvents = 'auto';
            }, 1000);
        };
    }

    console.log("PDF签名页面初始化完成");
});
</script>
{% endblock %} 