#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF手写签名功能验证脚本
验证所有URL引用是否正确
"""

import os
import sys

def check_file_content(file_path, checks):
    """检查文件内容"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n检查文件: {file_path}")
        
        all_passed = True
        for check in checks:
            check_type = check['type']
            pattern = check['pattern']
            expected = check.get('expected', True)
            description = check.get('description', pattern)
            
            if check_type == 'contains':
                found = pattern in content
                if found == expected:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ❌ {description} - 期望: {'存在' if expected else '不存在'}, 实际: {'存在' if found else '不存在'}")
                    all_passed = False
            
            elif check_type == 'not_contains':
                found = pattern in content
                if not found:
                    print(f"  ✅ 不包含: {description}")
                else:
                    print(f"  ❌ 仍然包含: {description}")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        return False

def main():
    """主验证函数"""
    print("PDF手写签名功能 - URL验证工具")
    print("=" * 50)
    
    # 定义检查规则
    file_checks = [
        {
            'file': 'app/routes.py',
            'checks': [
                {
                    'type': 'contains',
                    'pattern': 'def pdf_handwriting_signature',
                    'description': 'PDF手写签名路由定义'
                },
                {
                    'type': 'contains',
                    'pattern': 'def save_pdf_signature',
                    'description': '保存PDF签名路由定义'
                },
                {
                    'type': 'contains',
                    'pattern': 'def delete_pdf_signature',
                    'description': '删除PDF签名路由定义'
                },
                {
                    'type': 'contains',
                    'pattern': "url_for('main.preview_file'",
                    'description': '正确的预览文件URL引用'
                },
                {
                    'type': 'not_contains',
                    'pattern': "url_for('main.file_detail'",
                    'description': '错误的file_detail URL引用'
                }
            ]
        },
        {
            'file': 'app/templates/preview/pdf_sign.html',
            'checks': [
                {
                    'type': 'contains',
                    'pattern': "url_for('main.preview_file'",
                    'description': '返回按钮正确URL引用'
                },
                {
                    'type': 'contains',
                    'pattern': "url_for('main.stream_file'",
                    'description': 'PDF iframe正确URL引用'
                },
                {
                    'type': 'not_contains',
                    'pattern': "url_for('main.file_detail'",
                    'description': '错误的file_detail URL引用'
                },
                {
                    'type': 'not_contains',
                    'pattern': "url_for('main.file_preview'",
                    'description': '错误的file_preview URL引用'
                },
                {
                    'type': 'contains',
                    'pattern': 'id="signaturePad"',
                    'description': '签名板元素'
                },
                {
                    'type': 'contains',
                    'pattern': 'SignaturePad',
                    'description': 'SignaturePad库引用'
                }
            ]
        },
        {
            'file': 'app/templates/preview/pdf.html',
            'checks': [
                {
                    'type': 'contains',
                    'pattern': "url_for('main.pdf_handwriting_signature'",
                    'description': 'PDF手写签名按钮URL'
                },
                {
                    'type': 'contains',
                    'pattern': 'PDF手写签名',
                    'description': 'PDF手写签名按钮文本'
                }
            ]
        }
    ]
    
    # 执行检查
    all_passed = True
    for file_check in file_checks:
        file_path = file_check['file']
        checks = file_check['checks']
        
        if not check_file_content(file_path, checks):
            all_passed = False
    
    # 检查数据库迁移文件
    print(f"\n检查数据库迁移文件...")
    migration_file = 'migrations/add_signature_fields.sql'
    if os.path.exists(migration_file):
        print(f"✅ 数据库迁移文件存在: {migration_file}")
    else:
        print(f"❌ 数据库迁移文件不存在: {migration_file}")
        all_passed = False
    
    # 检查安装脚本
    print(f"\n检查安装脚本...")
    install_scripts = [
        'install_handwriting_signature.py',
        'update_database.py',
        'quick_fix_database.py'
    ]
    
    for script in install_scripts:
        if os.path.exists(script):
            print(f"✅ 安装脚本存在: {script}")
        else:
            print(f"⚠️ 安装脚本不存在: {script}")
    
    # 总结
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有检查通过！PDF手写签名功能准备就绪。")
        print("\n下一步:")
        print("1. 确保数据库已更新")
        print("2. 重启Flask应用")
        print("3. 测试PDF手写签名功能")
        print("4. 运行: python test_routes.py")
    else:
        print("❌ 部分检查失败，请修复上述问题。")
        print("\n故障排除:")
        print("1. 运行: python fix_url_issues.py")
        print("2. 检查文件是否存在")
        print("3. 验证URL引用是否正确")
    
    return all_passed

def quick_test():
    """快速测试关键功能"""
    print("\n快速功能测试...")
    
    # 检查关键文件是否存在
    key_files = [
        'app/routes.py',
        'app/templates/preview/pdf_sign.html',
        'app/templates/preview/pdf.html',
        'app/models.py'
    ]
    
    missing_files = []
    for file_path in key_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {missing_files}")
        return False
    
    print("✅ 所有关键文件都存在")
    
    # 检查路由定义
    try:
        with open('app/routes.py', 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        required_routes = [
            '@main.route(\'/file/pdf_signature/<int:file_id>\')',
            '@main.route(\'/file/save_pdf_signature/<int:file_id>\')',
            '@main.route(\'/file/delete_pdf_signature/<int:signature_id>\')'
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in routes_content:
                missing_routes.append(route)
        
        if missing_routes:
            print(f"❌ 缺少路由定义: {missing_routes}")
            return False
        
        print("✅ 所有必需路由都已定义")
        return True
        
    except Exception as e:
        print(f"❌ 检查路由失败: {e}")
        return False

if __name__ == "__main__":
    try:
        # 执行主验证
        main_success = main()
        
        # 执行快速测试
        quick_success = quick_test()
        
        overall_success = main_success and quick_success
        
        if overall_success:
            print("\n🎉 验证完成！PDF手写签名功能已准备就绪。")
        else:
            print("\n❌ 验证失败，请修复上述问题。")
        
        sys.exit(0 if overall_success else 1)
        
    except KeyboardInterrupt:
        print("\n\n验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n验证过程中发生错误: {e}")
        sys.exit(1)
