#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库更新脚本
自动为PDF手写签名功能更新数据库结构
"""

import mysql.connector
import sys
import os
from pathlib import Path

def list_databases(config):
    """列出可用的数据库"""
    try:
        # 创建不指定数据库的连接
        temp_config = config.copy()
        if 'database' in temp_config:
            del temp_config['database']

        conn = mysql.connector.connect(**temp_config)
        cursor = conn.cursor()

        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()

        print("\n可用的数据库:")
        for i, (db_name,) in enumerate(databases, 1):
            # 过滤系统数据库
            if db_name not in ['information_schema', 'performance_schema', 'mysql', 'sys']:
                print(f"  {i}. {db_name}")

        cursor.close()
        conn.close()
        return [db[0] for db in databases if db[0] not in ['information_schema', 'performance_schema', 'mysql', 'sys']]

    except Exception as e:
        print(f"无法列出数据库: {e}")
        return []

def get_database_config():
    """获取数据库配置"""
    print("请输入数据库连接信息:")

    config = {
        'host': input("数据库主机 (默认: localhost): ").strip() or 'localhost',
        'port': input("数据库端口 (默认: 3306): ").strip() or '3306',
        'user': input("数据库用户名 (默认: root): ").strip() or 'root',
        'password': input("数据库密码: ").strip()
    }

    config['port'] = int(config['port'])

    # 尝试列出可用数据库
    databases = list_databases(config)

    # 数据库名称不能为空
    while True:
        if databases:
            print(f"\n发现 {len(databases)} 个用户数据库")
            choice = input("输入数据库名称，或输入 'list' 查看可用数据库: ").strip()
            if choice.lower() == 'list':
                list_databases(config)
                continue
        else:
            choice = input("数据库名称 (必填): ").strip()

        if choice:
            config['database'] = choice
            break
        else:
            print("❌ 数据库名称不能为空，请重新输入")

    return config

def test_connection(config):
    """测试数据库连接"""
    try:
        conn = mysql.connector.connect(**config)
        print("✅ 数据库连接成功")
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"SHOW COLUMNS FROM {table_name} LIKE '{column_name}'")
    return cursor.fetchone() is not None

def update_database(config):
    """更新数据库结构"""
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        print(f"开始更新数据库: {config['database']}")
        
        # 检查signature表是否存在
        if not check_table_exists(cursor, 'signature'):
            print("❌ signature表不存在，请先创建基础表结构")
            return False
        
        print("✅ signature表存在")
        
        # 检查并添加signature_type字段
        if not check_column_exists(cursor, 'signature', 'signature_type'):
            print("添加signature_type字段...")
            cursor.execute("""
                ALTER TABLE signature 
                ADD COLUMN signature_type VARCHAR(50) DEFAULT 'handwriting' 
                COMMENT '签名类型：handwriting, digital, stamp'
            """)
            print("✅ signature_type字段添加成功")
        else:
            print("✅ signature_type字段已存在")
        
        # 检查并添加signature_position字段
        if not check_column_exists(cursor, 'signature', 'signature_position'):
            print("添加signature_position字段...")
            cursor.execute("""
                ALTER TABLE signature 
                ADD COLUMN signature_position VARCHAR(100) 
                COMMENT '签名在文档中的位置信息 (x,y,width,height)'
            """)
            print("✅ signature_position字段添加成功")
        else:
            print("✅ signature_position字段已存在")
        
        # 创建索引
        try:
            print("创建索引...")
            cursor.execute("CREATE INDEX idx_signature_type ON signature(signature_type)")
            print("✅ idx_signature_type索引创建成功")
        except mysql.connector.Error as e:
            if "Duplicate key name" in str(e):
                print("✅ idx_signature_type索引已存在")
            else:
                print(f"⚠️ 创建idx_signature_type索引失败: {e}")
        
        try:
            cursor.execute("CREATE INDEX idx_signature_file_type ON signature(file_id, signature_type)")
            print("✅ idx_signature_file_type索引创建成功")
        except mysql.connector.Error as e:
            if "Duplicate key name" in str(e):
                print("✅ idx_signature_file_type索引已存在")
            else:
                print(f"⚠️ 创建idx_signature_file_type索引失败: {e}")
        
        # 更新现有记录
        print("更新现有签名记录...")
        cursor.execute("UPDATE signature SET signature_type = 'handwriting' WHERE signature_type IS NULL")
        updated_rows = cursor.rowcount
        print(f"✅ 更新了 {updated_rows} 条记录")
        
        # 检查user_signature表
        if check_table_exists(cursor, 'user_signature'):
            print("✅ user_signature表存在")
        else:
            print("⚠️ user_signature表不存在，签名模板功能可能无法使用")
        
        # 提交更改
        conn.commit()
        print("✅ 数据库更新完成")
        
        # 验证更新结果
        print("\n验证更新结果:")
        cursor.execute("DESCRIBE signature")
        columns = cursor.fetchall()
        
        print("signature表结构:")
        for column in columns:
            print(f"  {column[0]}: {column[1]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def create_backup(config):
    """创建数据库备份"""
    try:
        import subprocess
        from datetime import datetime

        # 检查数据库名称
        if not config.get('database'):
            print("❌ 数据库名称为空，无法创建备份")
            return False

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_{config['database']}_{timestamp}.sql"

        # 构建mysqldump命令
        cmd = [
            "mysqldump",
            f"--host={config['host']}",
            f"--port={config['port']}",
            f"--user={config['user']}",
            f"--password={config['password']}",
            "--single-transaction",
            "--routines",
            "--triggers",
            config['database']
        ]

        print(f"创建数据库备份: {backup_file}")

        with open(backup_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)

        if result.returncode == 0:
            print(f"✅ 备份创建成功: {backup_file}")
            return True
        else:
            print(f"⚠️ 备份创建失败: {result.stderr}")
            # 如果备份失败，删除空文件
            try:
                os.remove(backup_file)
            except:
                pass
            return False

    except Exception as e:
        print(f"⚠️ 备份创建失败: {e}")
        return False

def main():
    """主函数"""
    print("PDF手写签名功能 - 数据库更新工具")
    print("=" * 50)
    
    # 获取数据库配置
    config = get_database_config()
    
    # 测试连接
    if not test_connection(config):
        print("请检查数据库配置后重试")
        return False
    
    # 询问是否创建备份
    create_backup_choice = input("\n是否创建数据库备份？(y/N): ").strip().lower()
    if create_backup_choice == 'y':
        create_backup(config)
    
    # 确认更新
    print(f"\n即将更新数据库: {config['database']}")
    print("将添加以下字段和索引:")
    print("- signature.signature_type (VARCHAR(50))")
    print("- signature.signature_position (VARCHAR(100))")
    print("- 相关索引")
    
    confirm = input("\n确认执行更新？(y/N): ").strip().lower()
    if confirm != 'y':
        print("更新已取消")
        return False
    
    # 执行更新
    success = update_database(config)
    
    if success:
        print("\n🎉 数据库更新成功！")
        print("现在可以使用PDF手写签名功能了。")
        print("\n下一步:")
        print("1. 启动Flask应用")
        print("2. 上传PDF文件测试签名功能")
        print("3. 运行测试脚本: python test_pdf_signature.py")
    else:
        print("\n❌ 数据库更新失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n更新被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n更新过程中发生错误: {e}")
        sys.exit(1)
