#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据库修复脚本
专门用于修复PDF手写签名功能的数据库问题
"""

import mysql.connector
import sys

def main():
    """主函数"""
    print("PDF手写签名功能 - 快速数据库修复")
    print("=" * 50)
    
    # 获取数据库配置
    print("请输入数据库连接信息:")
    host = input("数据库主机 (默认: localhost): ").strip() or 'localhost'
    port = input("数据库端口 (默认: 3306): ").strip() or '3306'
    user = input("数据库用户名 (默认: root): ").strip() or 'root'
    password = input("数据库密码: ").strip()
    
    try:
        port = int(port)
    except ValueError:
        print("❌ 端口号必须是数字")
        return False
    
    # 连接数据库并列出可用数据库
    try:
        print("\n正在连接数据库...")
        conn = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password
        )
        cursor = conn.cursor()
        
        # 列出可用数据库
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        
        user_databases = []
        print("\n可用的数据库:")
        for i, (db_name,) in enumerate(databases, 1):
            if db_name not in ['information_schema', 'performance_schema', 'mysql', 'sys']:
                user_databases.append(db_name)
                print(f"  {i}. {db_name}")
        
        if not user_databases:
            print("❌ 没有找到用户数据库")
            return False
        
        # 选择数据库
        while True:
            database = input(f"\n请选择数据库名称 (1-{len(user_databases)} 或直接输入名称): ").strip()
            
            if database.isdigit():
                idx = int(database) - 1
                if 0 <= idx < len(user_databases):
                    database = user_databases[idx]
                    break
                else:
                    print("❌ 无效的选择")
                    continue
            elif database in user_databases:
                break
            elif database:
                # 用户输入了不在列表中的数据库名
                confirm = input(f"数据库 '{database}' 不在列表中，确认使用？(y/N): ").strip().lower()
                if confirm == 'y':
                    break
                else:
                    continue
            else:
                print("❌ 请输入有效的数据库名称")
        
        # 连接到指定数据库
        cursor.close()
        conn.close()
        
        print(f"\n正在连接到数据库: {database}")
        conn = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查signature表是否存在
        cursor.execute("SHOW TABLES LIKE 'signature'")
        if not cursor.fetchone():
            print("❌ signature表不存在，请先创建基础表结构")
            return False
        
        print("✅ signature表存在")
        
        # 检查当前表结构
        cursor.execute("DESCRIBE signature")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        print("\n当前signature表结构:")
        for col in columns:
            print(f"  {col[0]}: {col[1]}")
        
        # 检查并添加缺失的字段
        updates_needed = []
        
        if 'signature_type' not in column_names:
            updates_needed.append("添加signature_type字段")
        
        if 'signature_position' not in column_names:
            updates_needed.append("添加signature_position字段")
        
        if not updates_needed:
            print("\n✅ 数据库结构已是最新，无需更新")
            return True
        
        print(f"\n需要执行的更新:")
        for update in updates_needed:
            print(f"  - {update}")
        
        confirm = input("\n确认执行更新？(y/N): ").strip().lower()
        if confirm != 'y':
            print("更新已取消")
            return False
        
        # 执行更新
        print("\n开始更新数据库...")
        
        try:
            # 添加signature_type字段
            if 'signature_type' not in column_names:
                print("添加signature_type字段...")
                cursor.execute("""
                    ALTER TABLE signature 
                    ADD COLUMN signature_type VARCHAR(50) DEFAULT 'handwriting' 
                    COMMENT '签名类型：handwriting, digital, stamp'
                """)
                print("✅ signature_type字段添加成功")
            
            # 添加signature_position字段
            if 'signature_position' not in column_names:
                print("添加signature_position字段...")
                cursor.execute("""
                    ALTER TABLE signature 
                    ADD COLUMN signature_position VARCHAR(100) 
                    COMMENT '签名在文档中的位置信息 (x,y,width,height)'
                """)
                print("✅ signature_position字段添加成功")
            
            # 创建索引
            try:
                print("创建索引...")
                cursor.execute("CREATE INDEX idx_signature_type ON signature(signature_type)")
                print("✅ idx_signature_type索引创建成功")
            except mysql.connector.Error as e:
                if "Duplicate key name" in str(e):
                    print("✅ idx_signature_type索引已存在")
                else:
                    print(f"⚠️ 创建idx_signature_type索引失败: {e}")
            
            try:
                cursor.execute("CREATE INDEX idx_signature_file_type ON signature(file_id, signature_type)")
                print("✅ idx_signature_file_type索引创建成功")
            except mysql.connector.Error as e:
                if "Duplicate key name" in str(e):
                    print("✅ idx_signature_file_type索引已存在")
                else:
                    print(f"⚠️ 创建idx_signature_file_type索引失败: {e}")
            
            # 更新现有记录
            print("更新现有签名记录...")
            cursor.execute("UPDATE signature SET signature_type = 'handwriting' WHERE signature_type IS NULL")
            updated_rows = cursor.rowcount
            print(f"✅ 更新了 {updated_rows} 条记录")
            
            # 提交更改
            conn.commit()
            print("\n🎉 数据库更新完成！")
            
            # 验证更新结果
            print("\n验证更新结果:")
            cursor.execute("DESCRIBE signature")
            columns = cursor.fetchall()
            
            print("更新后的signature表结构:")
            for column in columns:
                print(f"  {column[0]}: {column[1]}")
            
            print("\n✅ PDF手写签名功能数据库准备完成")
            print("现在可以启动应用并测试签名功能了！")
            
            return True
            
        except Exception as e:
            conn.rollback()
            print(f"❌ 数据库更新失败: {e}")
            return False
        
        finally:
            cursor.close()
            conn.close()
    
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n下一步:")
            print("1. 启动Flask应用: python app.py")
            print("2. 上传PDF文件测试签名功能")
            print("3. 运行测试脚本: python test_pdf_signature.py")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        sys.exit(1)
