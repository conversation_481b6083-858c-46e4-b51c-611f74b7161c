#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF手写签名功能安装脚本
适用于Windows开发环境和Ubuntu生产环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_step(step, message):
    """打印安装步骤"""
    print(f"\n{'='*60}")
    print(f"步骤 {step}: {message}")
    print('='*60)

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n执行: {command}")
    if description:
        print(f"描述: {description}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stderr:
            print(f"错误详情: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    print(f"Python版本: {version.major}.{version.minor}.{version.micro} ✓")
    return True

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        import mysql.connector
        print("MySQL连接器可用 ✓")
        return True
    except ImportError:
        print("警告: MySQL连接器未安装，将尝试安装...")
        return install_mysql_connector()

def install_mysql_connector():
    """安装MySQL连接器"""
    commands = [
        "pip install mysql-connector-python",
        "pip install PyMySQL"
    ]
    
    for cmd in commands:
        if run_command(cmd, "安装MySQL连接器"):
            return True
    return False

def install_dependencies():
    """安装依赖包"""
    print_step(1, "安装Python依赖包")
    
    # 基础依赖
    dependencies = [
        "Flask>=2.0.0",
        "Flask-SQLAlchemy>=2.5.0", 
        "Flask-Login>=0.5.0",
        "Pillow>=8.0.0",
        "pytz>=2021.1",
        "mysql-connector-python>=8.0.0",
        "PyMySQL>=1.0.0"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"安装 {dep}"):
            print(f"警告: {dep} 安装失败，请手动安装")
    
    print("依赖包安装完成 ✓")

def update_database():
    """更新数据库结构"""
    print_step(2, "更新数据库结构")
    
    migration_file = Path("migrations/add_signature_fields.sql")
    if not migration_file.exists():
        print("错误: 找不到数据库迁移文件")
        return False
    
    print("请手动执行以下SQL语句来更新数据库:")
    print("-" * 50)
    with open(migration_file, 'r', encoding='utf-8') as f:
        print(f.read())
    print("-" * 50)
    
    response = input("数据库更新完成后，请输入 'y' 继续: ")
    return response.lower() == 'y'

def check_file_structure():
    """检查文件结构"""
    print_step(3, "检查文件结构")
    
    required_files = [
        "app/models.py",
        "app/routes.py", 
        "app/templates/preview/pdf_sign.html",
        "migrations/add_signature_fields.sql"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print("\n缺少以下文件:")
        for file_path in missing_files:
            print(f"✗ {file_path}")
        return False
    
    print("文件结构检查完成 ✓")
    return True

def setup_static_files():
    """设置静态文件"""
    print_step(4, "设置静态文件")
    
    # 创建必要的目录
    directories = [
        "app/static/js",
        "app/static/css", 
        "app/static/uploads",
        "app/templates/preview"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {directory}")
    
    print("静态文件设置完成 ✓")

def test_signature_functionality():
    """测试签名功能"""
    print_step(5, "测试签名功能")
    
    print("请按照以下步骤测试签名功能:")
    print("1. 启动Flask应用")
    print("2. 上传一个PDF文件")
    print("3. 访问PDF文件详情页面")
    print("4. 点击'PDF手写签名'按钮")
    print("5. 测试手写签名功能")
    
    response = input("测试完成后，请输入 'y' 确认功能正常: ")
    return response.lower() == 'y'

def create_test_script():
    """创建测试脚本"""
    print_step(6, "创建测试脚本")
    
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
PDF手写签名功能测试脚本
\"\"\"

import requests
import json
import base64
from io import BytesIO
from PIL import Image

def test_signature_api():
    \"\"\"测试签名API\"\"\"
    base_url = "http://localhost:5000"  # 根据实际情况修改
    
    # 创建测试签名图像
    img = Image.new('RGB', (300, 150), color='white')
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
    
    # 测试保存签名
    data = {
        'signature_data': signature_data,
        'position_x': '100',
        'position_y': '100', 
        'signature_width': '150',
        'signature_height': '75',
        'save_as_template': 'true'
    }
    
    print("测试签名API...")
    print("请确保应用正在运行并且有可用的PDF文件")
    
if __name__ == "__main__":
    test_signature_api()
"""
    
    with open("test_signature.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("测试脚本已创建: test_signature.py")

def main():
    """主安装流程"""
    print("PDF手写签名功能安装程序")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查MySQL连接
    if not check_mysql_connection():
        print("MySQL连接检查失败，请确保MySQL已安装并配置正确")
        return False
    
    # 安装依赖
    install_dependencies()
    
    # 检查文件结构
    if not check_file_structure():
        print("文件结构检查失败，请确保所有必要文件都存在")
        return False
    
    # 更新数据库
    if not update_database():
        print("数据库更新失败")
        return False
    
    # 设置静态文件
    setup_static_files()
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "="*60)
    print("安装完成！")
    print("="*60)
    print("\n下一步:")
    print("1. 确保MySQL数据库已更新")
    print("2. 启动Flask应用")
    print("3. 测试PDF手写签名功能")
    print("4. 运行 python test_signature.py 进行API测试")
    
    print("\n使用说明:")
    print("- 访问PDF文件详情页面")
    print("- 点击'PDF手写签名'按钮")
    print("- 使用鼠标在签名板上绘制签名")
    print("- 点击PDF文档放置签名")
    print("- 可以拖拽、调整大小、删除签名")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        sys.exit(1)
