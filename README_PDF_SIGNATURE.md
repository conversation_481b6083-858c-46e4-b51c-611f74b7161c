# PDF手写签名功能

## 🎯 项目概述

为文件管理系统添加了完整的PDF手写签名功能，支持在Windows开发环境和Ubuntu生产环境中使用MySQL数据库。用户可以直接在PDF文档上进行手写签名，并支持签名的管理操作。

## ✨ 主要功能

### 核心特性
- 🖊️ **手写签名** - 使用鼠标或触控设备绘制签名
- 📍 **精确定位** - 点击PDF任意位置放置签名
- 🎛️ **签名管理** - 拖拽、调整大小、修改、删除
- 💾 **模板保存** - 保存常用签名模板
- 🔄 **多签名支持** - 单个PDF支持多个签名

### 技术特性
- 🌐 **跨平台兼容** - Windows开发 + Ubuntu生产
- 🗄️ **MySQL数据库** - 可靠的数据存储
- 📱 **响应式设计** - 支持桌面和移动设备
- 🔒 **安全控制** - 权限管理和操作日志

## 🚀 快速开始

### 1. 一键安装
```bash
python install_handwriting_signature.py
```

### 2. 数据库迁移
```sql
mysql -u username -p database_name < migrations/add_signature_fields.sql
```

### 3. 启动应用
```bash
python app.py
```

### 4. 测试功能
```bash
python test_pdf_signature.py
```

## 📁 文件结构

```
├── app/
│   ├── models.py                          # 数据库模型（已更新）
│   ├── routes.py                          # 路由处理（已更新）
│   └── templates/
│       └── preview/
│           ├── pdf.html                   # PDF预览页面（已更新）
│           └── pdf_sign.html              # PDF手写签名页面（新增）
├── migrations/
│   └── add_signature_fields.sql           # 数据库迁移脚本
├── install_handwriting_signature.py       # 安装脚本
├── test_pdf_signature.py                  # 测试脚本
├── PDF_HANDWRITING_SIGNATURE_GUIDE.md     # 详细使用指南
└── README_PDF_SIGNATURE.md                # 本文件
```

## 🎮 使用方法

### 添加签名
1. **上传PDF文件**到系统
2. **点击文件名**进入预览页面  
3. **点击"PDF手写签名"**按钮
4. **选择签名方式**：
   - 新建签名：点击"新建签名" → 点击PDF位置 → 绘制签名
   - 使用模板：选择已保存签名 → 点击PDF位置

### 管理签名
- **🖊️ 修改**：重新绘制签名内容
- **➕ 放大**：增大签名尺寸
- **➖ 缩小**：减小签名尺寸  
- **🗑️ 删除**：删除签名
- **🖱️ 拖拽**：移动签名位置

## 🛠️ 技术架构

### 前端技术
- **HTML5 Canvas** - 签名绘制
- **Bootstrap 5** - UI框架
- **Signature Pad** - 手写签名库
- **JavaScript ES6+** - 交互逻辑

### 后端技术
- **Flask** - Web框架
- **SQLAlchemy** - ORM
- **MySQL** - 数据库
- **Pillow** - 图像处理

### 数据库设计
```sql
-- 签名表
signature (
    id, user_id, file_id, 
    signature_data,           -- Base64图像
    signature_type,           -- 签名类型
    signature_position,       -- 位置信息
    signature_metadata        -- JSON元数据
)

-- 用户签名模板表  
user_signature (
    id, user_id,
    signature_data,           -- Base64模板
    description,              -- 描述
    is_default               -- 是否默认
)
```

## 🔧 API接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/file/pdf_signature/<file_id>` | 签名页面 |
| POST | `/file/save_pdf_signature/<file_id>` | 保存签名 |
| POST | `/file/delete_pdf_signature/<signature_id>` | 删除签名 |
| GET | `/file/get_pdf_signatures/<file_id>` | 获取签名列表 |

## 🔍 测试验证

### 自动化测试
```bash
# 运行完整测试套件
python test_pdf_signature.py

# 指定服务器地址测试
python test_pdf_signature.py http://your-server:5000
```

### 手动测试步骤
1. ✅ 上传PDF文件
2. ✅ 访问PDF预览页面
3. ✅ 点击"PDF手写签名"按钮
4. ✅ 测试新建签名功能
5. ✅ 测试已保存签名功能
6. ✅ 测试签名管理操作

## 🐛 故障排除

### 常见问题

**Q: 签名按钮不显示？**
```bash
# 检查路由配置
grep -n "pdf_handwriting_signature" app/routes.py
```

**Q: 数据库错误？**
```sql
-- 检查表结构
DESCRIBE signature;
DESCRIBE user_signature;
```

**Q: 签名板无法绘制？**
```javascript
// 浏览器控制台检查
console.log(typeof SignaturePad);
```

### 调试工具
- 浏览器开发者工具
- Flask应用日志
- MySQL错误日志
- 测试脚本输出

## 🔒 安全考虑

### 权限控制
- ✅ 文件访问权限验证
- ✅ 签名操作权限检查
- ✅ 用户身份认证

### 数据安全
- ✅ Base64编码存储
- ✅ 操作日志记录
- ✅ 输入数据验证

## 📈 性能优化

### 前端优化
- 图像压缩和缓存
- 懒加载签名数据
- 响应式布局

### 后端优化
- 数据库索引优化
- 连接池管理
- 异步处理

### 数据库优化
```sql
-- 创建性能索引
CREATE INDEX idx_signature_type ON signature(signature_type);
CREATE INDEX idx_signature_file_type ON signature(file_id, signature_type);
```

## 🚀 部署建议

### 开发环境（Windows）
```bash
# 安装依赖
pip install -r requirements.txt

# 配置数据库
mysql -u root -p < migrations/add_signature_fields.sql

# 启动开发服务器
python app.py
```

### 生产环境（Ubuntu）
```bash
# 使用虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置Nginx（可选）
sudo nginx -t && sudo systemctl reload nginx

# 使用Gunicorn启动
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 📋 版本信息

- **版本**: 1.0.0
- **发布日期**: 2024年
- **兼容性**: Python 3.7+, MySQL 5.7+
- **浏览器支持**: Chrome, Firefox, Safari, Edge

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情

## 📞 技术支持

如遇问题，请：
1. 查看详细文档：`PDF_HANDWRITING_SIGNATURE_GUIDE.md`
2. 运行测试脚本：`python test_pdf_signature.py`
3. 检查系统日志和数据库状态
4. 提交Issue描述问题

---

**🎉 恭喜！PDF手写签名功能已成功集成到您的文件管理系统中。**
