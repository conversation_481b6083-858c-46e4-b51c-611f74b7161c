@echo off
chcp 65001 > nul
echo 正在启动文件管理系统...
echo.

REM 检查是否存在Python环境
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 未找到Python，请确保已安装Python并添加到PATH环境变量中。
    echo 您可以从 https://www.python.org/downloads/ 下载并安装Python。
    echo.
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

REM 检查是否已安装Flask
python -c "import flask" >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装Flask...
    pip install flask flask-sqlalchemy flask-login pillow img2pdf
)

REM 设置Flask环境变量
set FLASK_APP=app
set FLASK_ENV=development
set FLASK_DEBUG=1

REM 运行数据库迁移脚本
echo 正在应用数据库迁移...
python migrations/add_signature_fields.py

REM 启动Flask应用
echo 正在启动应用服务器...
echo 如果启动失败，请确保已安装Flask和其他依赖项:
echo pip install flask flask-sqlalchemy flask-login pillow img2pdf
echo.

python -m flask run --host=0.0.0.0

echo.
echo 应用已停止运行，按任意键退出...
pause > nul 