#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路由测试脚本
验证PDF手写签名相关路由是否正常工作
"""

import requests
import sys

def test_routes(base_url="http://localhost:5000"):
    """测试路由"""
    print("PDF手写签名路由测试")
    print("=" * 50)
    print(f"测试服务器: {base_url}")
    
    # 测试路由列表
    test_routes = [
        {
            'name': '主页',
            'url': '/',
            'method': 'GET',
            'expected_status': [200, 302]  # 可能重定向到登录页
        },
        {
            'name': '登录页面',
            'url': '/login',
            'method': 'GET',
            'expected_status': [200]
        },
        {
            'name': 'PDF预览页面',
            'url': '/file/preview/1',  # 假设文件ID为1
            'method': 'GET',
            'expected_status': [200, 302, 404]  # 可能需要登录或文件不存在
        },
        {
            'name': 'PDF手写签名页面',
            'url': '/file/pdf_signature/1',  # 假设文件ID为1
            'method': 'GET',
            'expected_status': [200, 302, 404]  # 可能需要登录或文件不存在
        }
    ]
    
    session = requests.Session()
    results = []
    
    for route in test_routes:
        try:
            print(f"\n测试: {route['name']}")
            print(f"URL: {route['url']}")
            
            if route['method'] == 'GET':
                response = session.get(f"{base_url}{route['url']}", timeout=10)
            elif route['method'] == 'POST':
                response = session.post(f"{base_url}{route['url']}", timeout=10)
            
            status_code = response.status_code
            print(f"状态码: {status_code}")
            
            if status_code in route['expected_status']:
                print("✅ 测试通过")
                results.append(True)
            else:
                print(f"❌ 测试失败 - 期望状态码: {route['expected_status']}, 实际: {status_code}")
                results.append(False)
                
            # 检查响应内容
            if status_code == 200:
                content_length = len(response.text)
                print(f"响应内容长度: {content_length} 字符")
                
                # 检查是否包含错误信息
                if 'error' in response.text.lower() or 'exception' in response.text.lower():
                    print("⚠️ 响应中可能包含错误信息")
                
            elif status_code == 302:
                location = response.headers.get('Location', '')
                print(f"重定向到: {location}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 服务器可能未启动")
            results.append(False)
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            results.append(False)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有路由测试通过！")
    else:
        print("⚠️ 部分路由测试失败")
    
    return passed == total

def check_flask_app():
    """检查Flask应用是否正在运行"""
    print("检查Flask应用状态...")
    
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ Flask应用正在运行")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Flask应用未运行")
        print("请先启动Flask应用: python app.py")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("PDF手写签名功能 - 路由测试工具")
    print("=" * 60)
    
    # 检查Flask应用
    if not check_flask_app():
        print("\n请按以下步骤启动应用:")
        print("1. 确保数据库已更新")
        print("2. 运行: python app.py")
        print("3. 重新运行此测试脚本")
        return False
    
    # 测试路由
    success = test_routes()
    
    if success:
        print("\n下一步测试:")
        print("1. 登录系统")
        print("2. 上传一个PDF文件")
        print("3. 点击PDF文件进入预览页面")
        print("4. 点击'PDF手写签名'按钮")
        print("5. 测试手写签名功能")
    else:
        print("\n故障排除:")
        print("1. 检查Flask应用是否正常启动")
        print("2. 检查数据库连接是否正常")
        print("3. 检查路由定义是否正确")
        print("4. 查看Flask应用日志")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
