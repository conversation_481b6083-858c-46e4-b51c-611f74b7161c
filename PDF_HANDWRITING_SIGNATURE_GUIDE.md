# PDF手写签名功能使用指南

## 功能概述

本系统新增了PDF手写签名功能，支持在Windows开发环境和Ubuntu生产环境中使用MySQL数据库。用户可以直接在PDF文档上进行手写签名，支持签名的拖拽、调整大小、修改和删除等操作。

## 主要特性

### ✅ 核心功能
- **手写签名**：使用鼠标或触控设备在签名板上绘制签名
- **位置选择**：点击PDF文档任意位置放置签名
- **签名管理**：拖拽移动、放大缩小、修改内容、删除签名
- **模板保存**：将签名保存为模板，方便重复使用
- **多签名支持**：一个PDF文档可以添加多个签名

### ✅ 技术特性
- **跨平台兼容**：Windows开发 + Ubuntu生产环境
- **数据库支持**：MySQL数据库存储签名数据
- **响应式设计**：支持桌面和移动设备
- **安全性**：权限控制和操作日志记录

## 安装部署

### 1. 环境要求

**开发环境（Windows）：**
- Python 3.7+
- MySQL 5.7+ 或 8.0+
- Flask 2.0+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

**生产环境（Ubuntu）：**
- Ubuntu 18.04+ 或 20.04+
- Python 3.7+
- MySQL 5.7+ 或 8.0+
- Nginx（可选，用于反向代理）

### 2. 快速安装

```bash
# 1. 运行安装脚本
python install_handwriting_signature.py

# 2. 手动执行数据库迁移
mysql -u username -p database_name < migrations/add_signature_fields.sql

# 3. 启动应用
python app.py
```

### 3. 手动安装步骤

**步骤1：安装Python依赖**
```bash
pip install Flask>=2.0.0
pip install Flask-SQLAlchemy>=2.5.0
pip install Flask-Login>=0.5.0
pip install Pillow>=8.0.0
pip install pytz>=2021.1
pip install mysql-connector-python>=8.0.0
```

**步骤2：更新数据库结构**
```sql
-- 为Signature表添加新字段
ALTER TABLE signature 
ADD COLUMN signature_type VARCHAR(50) DEFAULT 'handwriting';

ALTER TABLE signature 
ADD COLUMN signature_position VARCHAR(100);

-- 创建索引
CREATE INDEX idx_signature_type ON signature(signature_type);
CREATE INDEX idx_signature_file_type ON signature(file_id, signature_type);
```

**步骤3：验证安装**
- 启动Flask应用
- 上传一个PDF文件
- 访问PDF预览页面
- 点击"PDF手写签名"按钮测试功能

## 使用方法

### 1. 访问签名功能

1. **上传PDF文件**到系统
2. **点击文件名**进入预览页面
3. **点击"PDF手写签名"按钮**进入签名页面

### 2. 添加手写签名

**方法一：新建签名**
1. 点击"新建签名"按钮
2. 鼠标变为十字形，点击PDF上想要放置签名的位置
3. 在弹出的签名板上用鼠标绘制签名
4. 可选择"保存为签名模板"
5. 点击"确认签名"完成

**方法二：使用已保存签名**
1. 在下拉框中选择已保存的签名
2. 鼠标变为十字形，点击PDF上想要放置的位置
3. 签名自动添加到指定位置

### 3. 管理签名

每个签名都有控制按钮：

- **🖊️ 修改**（黄色）：重新绘制签名内容
- **➕ 放大**（绿色）：增大签名尺寸（最大400px）
- **➖ 缩小**（蓝色）：减小签名尺寸（最小50px）
- **🗑️ 删除**（红色）：删除签名

### 4. 拖拽移动

- 直接用鼠标拖拽签名到新位置
- 签名会自动限制在PDF文档范围内
- 拖拽时签名显示在最前面

### 5. 个性化设置

- **笔迹颜色**：选择签名的颜色
- **笔迹粗细**：调整签名线条的粗细（1-10px）
- **签名模板**：保存常用签名以便重复使用

## 技术架构

### 1. 前端技术栈

- **HTML5 Canvas**：签名板绘制
- **Bootstrap 5**：响应式UI框架
- **Font Awesome**：图标库
- **Signature Pad**：手写签名库
- **JavaScript ES6+**：交互逻辑

### 2. 后端技术栈

- **Flask**：Web框架
- **SQLAlchemy**：ORM数据库操作
- **MySQL**：数据存储
- **Pillow**：图像处理
- **pytz**：时区处理

### 3. 数据库设计

**Signature表结构：**
```sql
CREATE TABLE signature (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    file_id INT NOT NULL,
    signature_data TEXT NOT NULL,           -- Base64签名图像
    signature_date DATETIME DEFAULT NOW(),
    signature_metadata TEXT,               -- JSON元数据
    signature_type VARCHAR(50) DEFAULT 'handwriting',
    signature_position VARCHAR(100),       -- 位置信息 "x,y,width,height"
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (file_id) REFERENCES file(id)
);
```

**UserSignature表结构：**
```sql
CREATE TABLE user_signature (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    signature_data TEXT NOT NULL,          -- Base64签名模板
    created_at DATETIME DEFAULT NOW(),
    is_default BOOLEAN DEFAULT FALSE,
    description TEXT,
    FOREIGN KEY (user_id) REFERENCES user(id)
);
```

### 4. API接口

**主要路由：**
- `GET /file/pdf_signature/<file_id>` - 签名页面
- `POST /file/save_pdf_signature/<file_id>` - 保存签名
- `POST /file/delete_pdf_signature/<signature_id>` - 删除签名
- `GET /file/get_pdf_signatures/<file_id>` - 获取签名列表

## 故障排除

### 常见问题

**Q1: 点击"PDF手写签名"按钮没有反应？**
A: 检查路由是否正确配置，确保 `pdf_handwriting_signature` 路由存在

**Q2: 签名板不显示或无法绘制？**
A: 检查浏览器是否支持HTML5 Canvas，确保Signature Pad库正确加载

**Q3: 签名保存失败？**
A: 检查数据库连接，确保signature表有新增的字段

**Q4: 权限错误？**
A: 确保用户有访问文件的权限，检查文件夹权限设置

### 调试方法

**1. 浏览器控制台调试**
```javascript
// 打开浏览器开发者工具，查看控制台输出
console.log("检查签名功能状态");
```

**2. 服务器日志检查**
```bash
# 查看Flask应用日志
tail -f app.log

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

**3. 数据库检查**
```sql
-- 检查签名数据
SELECT * FROM signature WHERE signature_type = 'handwriting';

-- 检查用户签名模板
SELECT * FROM user_signature WHERE user_id = YOUR_USER_ID;
```

## 性能优化

### 1. 前端优化

- **图像压缩**：签名图像自动压缩为PNG格式
- **懒加载**：按需加载签名数据
- **缓存策略**：浏览器缓存静态资源

### 2. 后端优化

- **数据库索引**：为常用查询字段创建索引
- **连接池**：使用数据库连接池
- **异步处理**：大文件处理使用异步任务

### 3. 数据库优化

```sql
-- 创建复合索引
CREATE INDEX idx_signature_user_file ON signature(user_id, file_id);
CREATE INDEX idx_signature_date ON signature(signature_date);

-- 定期清理过期数据
DELETE FROM signature WHERE signature_date < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

## 安全考虑

### 1. 权限控制

- 用户只能为有权限访问的文件添加签名
- 只有签名创建者和管理员可以删除签名
- 文件夹权限继承到签名功能

### 2. 数据安全

- 签名数据使用Base64编码存储
- 操作日志记录所有签名相关操作
- IP地址和用户代理记录在元数据中

### 3. 输入验证

- 签名数据格式验证
- 位置坐标范围检查
- 文件类型限制（仅PDF）

## 扩展功能

### 未来可能的增强

1. **批量签名**：一次为多个PDF文件添加签名
2. **签名验证**：数字签名验证功能
3. **签名模板管理**：更丰富的模板管理界面
4. **移动端优化**：触控设备的手写体验优化
5. **OCR识别**：手写签名的文字识别

### 自定义开发

如需自定义功能，可以：

1. **修改签名样式**：编辑CSS样式文件
2. **添加新的签名类型**：扩展signature_type字段
3. **集成第三方服务**：如电子签名验证服务
4. **API扩展**：添加更多的RESTful API接口

## 支持与维护

### 技术支持

- 查看系统日志获取错误信息
- 使用浏览器开发者工具调试前端问题
- 检查数据库连接和表结构

### 版本更新

定期检查以下组件的更新：
- Flask框架
- Signature Pad库
- Bootstrap框架
- MySQL驱动

### 备份建议

- 定期备份signature和user_signature表
- 备份签名相关的静态文件
- 保存数据库结构变更记录

---

**注意事项：**
- 确保在生产环境中使用HTTPS协议
- 定期更新依赖库以修复安全漏洞
- 监控系统性能和存储空间使用情况
