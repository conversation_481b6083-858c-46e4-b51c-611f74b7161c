@echo off
REM 电子签名功能部署脚本 (Windows版本)
REM 用于在Windows环境下部署新的电子签名功能

setlocal enabledelayedexpansion

echo ==========================================
echo 电子签名功能部署脚本 (Windows)
echo ==========================================

REM 检查Python环境
echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo [SUCCESS] Python环境检查完成

REM 检查项目目录
echo [INFO] 检查项目目录...
if not exist "app\__init__.py" (
    echo [ERROR] 当前目录不是有效的Flask项目目录
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo [ERROR] 未找到requirements.txt文件
    pause
    exit /b 1
)

echo [SUCCESS] 项目目录检查完成

REM 创建备份目录
echo [INFO] 创建备份...
set BACKUP_DIR=backups\%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%_signature_deploy
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir "%BACKUP_DIR%" 2>nul

REM 备份关键文件
if exist "app\routes.py" (
    copy "app\routes.py" "%BACKUP_DIR%\routes.py.bak" >nul
    echo [INFO] 已备份 routes.py
)

if exist "app\templates\preview\pdf_sign.html" (
    copy "app\templates\preview\pdf_sign.html" "%BACKUP_DIR%\pdf_sign.html.bak" >nul
    echo [INFO] 已备份 pdf_sign.html
)

if exist "instance\file_manager.db" (
    copy "instance\file_manager.db" "%BACKUP_DIR%\file_manager.db.bak" >nul
    echo [INFO] 已备份数据库文件
)

echo [SUCCESS] 文件备份完成，备份目录：%BACKUP_DIR%

REM 安装依赖
echo [INFO] 安装Python依赖...

REM 检查虚拟环境
if not exist "venv" (
    echo [INFO] 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境并安装依赖
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)

echo [SUCCESS] 依赖安装完成

REM 检查数据库结构
echo [INFO] 检查数据库结构...
python -c "
from app import create_app, db
from app.models import User, Signature, UserSignature
import sys

app = create_app()
with app.app_context():
    try:
        # 检查表是否存在
        db.engine.execute('SELECT 1 FROM user LIMIT 1')
        print('✓ user表存在')
        
        db.engine.execute('SELECT 1 FROM signature LIMIT 1')
        print('✓ signature表存在')
        
        db.engine.execute('SELECT 1 FROM user_signature LIMIT 1')
        print('✓ user_signature表存在')
        
        print('数据库结构检查完成')
    except Exception as e:
        print(f'数据库检查失败: {e}')
        sys.exit(1)
"

if errorlevel 1 (
    echo [ERROR] 数据库结构检查失败
    pause
    exit /b 1
)

echo [SUCCESS] 数据库结构检查完成

REM 运行数据库迁移（如果存在）
if exist "migrations" (
    echo [INFO] 运行数据库迁移...
    flask db upgrade
    echo [SUCCESS] 数据库迁移完成
) else (
    echo [WARNING] 未找到migrations目录，跳过数据库迁移
)

REM 测试功能
if exist "test_signature_functionality.py" (
    echo [INFO] 测试电子签名功能...
    python test_signature_functionality.py
    if errorlevel 1 (
        echo [WARNING] 功能测试未完全通过，请检查日志
    ) else (
        echo [SUCCESS] 功能测试通过
    )
) else (
    echo [WARNING] 未找到测试脚本，跳过功能测试
)

REM 停止现有进程
echo [INFO] 停止现有进程...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

REM 启动应用
echo [INFO] 启动应用...
start /b python run.py

REM 等待服务启动
timeout /t 5 /nobreak >nul

REM 检查端口
netstat -an | findstr ":2026" >nul
if errorlevel 1 (
    echo [WARNING] 应用可能未正常启动，请检查日志
) else (
    echo [SUCCESS] 应用正在监听端口2026
)

REM 显示结果
echo.
echo ==========================================
echo 部署完成
echo ==========================================
echo.
echo 📋 部署摘要：
echo   • 电子签名功能已更新
echo   • 数据库结构已检查
echo   • 应用服务已重启
echo.
echo 🌐 访问地址：
echo   • 主页: http://localhost:2026
echo   • 签名管理: http://localhost:2026/user/signatures
echo.
echo 📁 重要文件：
echo   • 日志文件: logs\app.log
echo   • 配置文件: instance\config.py
echo   • 备份目录: %BACKUP_DIR%
echo.
echo 🔧 后续操作：
echo   1. 测试电子签名功能
echo   2. 检查用户权限设置
echo   3. 监控系统日志
echo.
echo [SUCCESS] 部署脚本执行完成！

REM 停用虚拟环境
deactivate

echo.
echo 按任意键退出...
pause >nul
