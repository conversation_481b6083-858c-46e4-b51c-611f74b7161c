#!/bin/bash

# 文件签名修复脚本 - Ubuntu版本
# 修复UserSignature缺少signature_id字段、File缺少physical_path字段
# 以及Signature缺少signature_type字段的问题

echo "开始修复签名表问题..."

# 进入项目目录
cd "$(dirname "$0")"

# 检查是否在虚拟环境中
if [ -z "$VIRTUAL_ENV" ]; then
    echo "警告：未检测到虚拟环境，脚本可能无法正常工作"
    echo "建议在激活paperless虚拟环境后运行此脚本"
    echo "您可以使用 'source /path/to/paperless/env/bin/activate' 激活环境"
    read -p "是否继续执行？(y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "已取消执行"
        exit 1
    fi
fi

# 1. 直接使用SQL语句修复数据库表结构
echo "正在修复数据库表结构..."

# 获取数据库连接信息
DB_USER="fileman"
DB_PASS="CV24051zhou"
DB_NAME="file_manager"
DB_HOST="localhost"

# 执行MySQL命令修复表结构 - 使用兼容的语法
mysql -u $DB_USER -p$DB_PASS -h $DB_HOST $DB_NAME <<EOF
-- 检查user_signature表是否有signature_id字段，如果没有则添加
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'user_signature' AND COLUMN_NAME = 'signature_id';

SET @add_column = CONCAT('ALTER TABLE user_signature ADD COLUMN signature_id INT');

SET @sql_text = IF(@column_exists = 0, @add_column, 'SELECT "UserSignature表已包含signature_id字段"');

PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查file表是否有physical_path字段，如果没有则添加
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'file' AND COLUMN_NAME = 'physical_path';

SET @add_column = CONCAT('ALTER TABLE file ADD COLUMN physical_path VARCHAR(255)');

SET @sql_text = IF(@column_exists = 0, @add_column, 'SELECT "File表已包含physical_path字段"');

PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查signature表是否有signature_type字段，如果没有则添加
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'signature' AND COLUMN_NAME = 'signature_type';

SET @add_column = CONCAT('ALTER TABLE signature ADD COLUMN signature_type VARCHAR(50) DEFAULT "handwriting"');

SET @sql_text = IF(@column_exists = 0, @add_column, 'SELECT "Signature表已包含signature_type字段"');

PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查signature表是否有signature_position字段，如果没有则添加
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'signature' AND COLUMN_NAME = 'signature_position';

SET @add_column = CONCAT('ALTER TABLE signature ADD COLUMN signature_position VARCHAR(100)');

SET @sql_text = IF(@column_exists = 0, @add_column, 'SELECT "Signature表已包含signature_position字段"');

PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加外键约束（可选）
SELECT COUNT(*) INTO @constraint_exists FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'user_signature' 
AND COLUMN_NAME = 'signature_id' AND REFERENCED_TABLE_NAME = 'signature';

SET @add_fk = CONCAT('ALTER TABLE user_signature ADD CONSTRAINT fk_user_signature_signature FOREIGN KEY (signature_id) REFERENCES signature(id)');

SET @sql_text = IF(@constraint_exists = 0 AND @column_exists = 0, @add_fk, 'SELECT "无需添加外键约束"');

PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
EOF

if [ $? -eq 0 ]; then
    echo "数据库表结构修复成功"
else
    echo "数据库表结构修复失败，请检查MySQL错误"
    exit 1
fi

# 2. 验证数据库修复
echo "验证数据库修复..."
python3 -c "
import sys
import os
import pymysql

try:
    # 连接数据库
    conn = pymysql.connect(
        host='localhost',
        user='fileman',
        password='CV24051zhou',
        database='file_manager'
    )
    
    # 检查user_signature表是否有signature_id字段
    with conn.cursor() as cursor:
        cursor.execute(\"\"\"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'file_manager' 
        AND TABLE_NAME = 'user_signature' 
        AND COLUMN_NAME = 'signature_id'
        \"\"\")
        result = cursor.fetchone()
        if result[0] > 0:
            print('UserSignature表已包含signature_id字段')
        else:
            print('警告: UserSignature表缺少signature_id字段')
            
        # 检查file表是否有physical_path字段
        cursor.execute(\"\"\"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'file_manager' 
        AND TABLE_NAME = 'file' 
        AND COLUMN_NAME = 'physical_path'
        \"\"\")
        result = cursor.fetchone()
        if result[0] > 0:
            print('File表已包含physical_path字段')
        else:
            print('警告: File表缺少physical_path字段')
            
        # 检查signature表是否有signature_type字段
        cursor.execute(\"\"\"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'file_manager' 
        AND TABLE_NAME = 'signature' 
        AND COLUMN_NAME = 'signature_type'
        \"\"\")
        result = cursor.fetchone()
        if result[0] > 0:
            print('Signature表已包含signature_type字段')
        else:
            print('警告: Signature表缺少signature_type字段')
            
    conn.close()
    print('数据库验证完成')
except Exception as e:
    print(f'验证数据库时出错: {str(e)}')
    sys.exit(1)
"

# 3. 重启应用
echo "修复完成，正在重启应用..."
./stop.sh
sleep 2
./start_prod.sh

echo "签名修复脚本已完成" 