# Ubuntu部署指南 - 电子签名功能

本指南详细说明如何在Ubuntu服务器上部署带有电子签名功能的文件管理系统。

## 系统要求

- Ubuntu 18.04 LTS 或更高版本
- Python 3.8 或更高版本
- MySQL 8.0 或更高版本
- 至少 2GB RAM
- 至少 10GB 可用磁盘空间

## 快速部署

### 1. 系统准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要的系统包
sudo apt install -y python3 python3-pip python3-venv python3-dev
sudo apt install -y mysql-server mysql-client
sudo apt install -y nginx supervisor
sudo apt install -y git curl wget unzip
```

### 2. 配置MySQL数据库

```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置MySQL
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

在MySQL命令行中执行：

```sql
CREATE DATABASE file_manager_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'file_manager'@'localhost' IDENTIFIED BY 'your_strong_password_here';
GRANT ALL PRIVILEGES ON file_manager_db.* TO 'file_manager'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 部署应用

```bash
# 克隆或上传项目代码
cd /opt
sudo mkdir file-manager
sudo chown $USER:$USER file-manager
cd file-manager

# 如果从Windows传输文件，确保文件权限正确
find . -type f -name "*.py" -exec chmod 644 {} \;
find . -type f -name "*.sh" -exec chmod +x {} \;

# 运行自动部署脚本
./deploy_signature_features.sh
```

### 4. 配置MySQL连接

```bash
# 复制MySQL配置文件
cp config_mysql_example.py instance/config.py

# 编辑配置文件
nano instance/config.py
```

修改以下配置项：
```python
MYSQL_PASSWORD = 'your_strong_password_here'  # 替换为实际密码
SECRET_KEY = 'your_secret_key_here'           # 生成强随机密钥
```

### 5. 初始化数据库

```bash
# 激活虚拟环境
source venv/bin/activate

# 设置Flask应用
export FLASK_APP=run.py

# 初始化数据库迁移
flask db init
flask db migrate -m "Initial migration with signature features"
flask db upgrade

# 创建管理员用户
python create_admin.py
```

### 6. 配置Nginx反向代理

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/file-manager
```

添加以下内容：

```nginx
server {
    listen 80;
    server_name your_domain.com;  # 替换为实际域名或IP

    client_max_body_size 1600M;
    
    location / {
        proxy_pass http://127.0.0.1:2026;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    location /static {
        alias /opt/file-manager/app/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads {
        alias /opt/file-manager/uploads;
        expires 1d;
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/file-manager /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. 配置Supervisor进程管理

创建Supervisor配置：

```bash
sudo nano /etc/supervisor/conf.d/file-manager.conf
```

添加以下内容：

```ini
[program:file-manager]
command=/opt/file-manager/venv/bin/python run.py
directory=/opt/file-manager
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/file-manager/logs/supervisor.log
environment=FLASK_ENV="production"
```

启动服务：

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start file-manager
```

## 手动部署步骤

如果自动部署脚本失败，可以按照以下步骤手动部署：

### 1. 创建虚拟环境

```bash
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
```

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

```bash
export FLASK_APP=run.py
export FLASK_ENV=production
export MYSQL_PASSWORD=your_password
export SECRET_KEY=your_secret_key
```

### 4. 初始化数据库

```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 5. 创建必要目录

```bash
mkdir -p logs uploads instance backups flask_session
chmod 755 logs uploads instance backups flask_session
```

### 6. 启动应用

```bash
python run.py
```

## 安全配置

### 1. 防火墙设置

```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL证书配置（推荐）

使用Let's Encrypt免费SSL证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your_domain.com
```

### 3. 文件权限设置

```bash
# 设置应用目录权限
sudo chown -R www-data:www-data /opt/file-manager
sudo chmod -R 755 /opt/file-manager
sudo chmod -R 777 /opt/file-manager/uploads
sudo chmod -R 777 /opt/file-manager/logs
```

## 监控和维护

### 1. 查看应用日志

```bash
# 应用日志
tail -f /opt/file-manager/logs/app.log

# Supervisor日志
tail -f /opt/file-manager/logs/supervisor.log

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. 重启服务

```bash
# 重启应用
sudo supervisorctl restart file-manager

# 重启Nginx
sudo systemctl restart nginx

# 重启MySQL
sudo systemctl restart mysql
```

### 3. 数据库备份

创建备份脚本：

```bash
nano backup_db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/file-manager/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u file_manager -p file_manager_db > "$BACKUP_DIR/db_backup_$DATE.sql"
find "$BACKUP_DIR" -name "db_backup_*.sql" -mtime +7 -delete
```

设置定时备份：

```bash
chmod +x backup_db.sh
crontab -e
# 添加：0 2 * * * /opt/file-manager/backup_db.sh
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务状态：`sudo systemctl status mysql`
   - 验证数据库凭据：`mysql -u file_manager -p`

2. **应用无法启动**
   - 检查Python依赖：`pip list`
   - 查看错误日志：`tail -f logs/app.log`

3. **文件上传失败**
   - 检查uploads目录权限：`ls -la uploads/`
   - 确认Nginx配置中的client_max_body_size

4. **签名功能异常**
   - 检查数据库表结构：确保signature和user_signature表存在
   - 验证JavaScript控制台是否有错误

### 性能优化

1. **数据库优化**
   ```sql
   # 在MySQL中执行
   CREATE INDEX idx_signature_user_id ON signature(user_id);
   CREATE INDEX idx_signature_file_id ON signature(file_id);
   CREATE INDEX idx_user_signature_user_id ON user_signature(user_id);
   ```

2. **静态文件缓存**
   - 配置Nginx静态文件缓存
   - 使用CDN加速静态资源

3. **应用缓存**
   - 配置Redis缓存
   - 启用Flask缓存

## 更新部署

当有新版本时：

```bash
# 备份当前版本
cp -r /opt/file-manager /opt/file-manager.backup.$(date +%Y%m%d)

# 更新代码
cd /opt/file-manager
git pull  # 或者上传新文件

# 更新依赖
source venv/bin/activate
pip install -r requirements.txt

# 运行数据库迁移
flask db upgrade

# 重启服务
sudo supervisorctl restart file-manager
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 检查日志文件获取详细错误信息
2. 确认所有依赖都已正确安装
3. 验证数据库连接和权限设置
4. 联系技术支持团队
