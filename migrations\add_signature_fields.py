"""
数据库迁移脚本 - 添加签名相关字段
适用于不同环境(Windows/Ubuntu)和不同项目结构
"""

import os
import sys
import sqlite3
import traceback

def find_app_path():
    """尝试查找应用程序路径并添加到Python路径"""
    # 确保能找到app模块或paperless模块
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    print(f"当前目录: {current_dir}")
    print(f"父目录: {parent_dir}")
    
    # 添加父目录到Python路径
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
        print(f"已添加 {parent_dir} 到Python路径")
    
    # 检查是否能导入app或paperless模块
    try:
        import app
        print("成功导入app模块")
        return 'app'
    except ImportError:
        print("无法导入app模块，尝试导入paperless模块")
        
    try:
        import paperless
        print("成功导入paperless模块")
        return 'paperless'
    except ImportError:
        print("无法导入paperless模块")
        
    print("警告: 无法找到应用程序模块")
    return None

def find_db_path():
    """查找数据库文件"""
    possible_locations = [
        'instance/app.db',                    # Flask默认位置
        'instance/paperless.db',              # 可能的替代名称
        '../instance/app.db',                 # 相对于migrations目录
        '../instance/paperless.db',           # 相对于migrations目录
        'app/app.db',                         # 直接在app目录下
        'paperless/paperless.db',             # 直接在paperless目录下
    ]
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    for location in possible_locations:
        # 尝试绝对路径
        abs_path = os.path.join(parent_dir, location)
        if os.path.exists(abs_path):
            print(f"找到数据库: {abs_path}")
            return abs_path
            
        # 尝试相对于当前目录的路径
        rel_path = os.path.join(current_dir, location)
        if os.path.exists(rel_path):
            print(f"找到数据库: {rel_path}")
            return rel_path
    
    print("警告: 未找到数据库文件")
    return None

def upgrade_with_direct_sqlite():
    """使用SQLite直接修改数据库"""
    db_path = find_db_path()
    if not db_path:
        print("错误: 找不到数据库文件，无法继续")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("数据库表:", [t[0] for t in tables])

        # 检查signature表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='signature';")
        if cursor.fetchone():
            print("检查signature表结构...")
            # 检查signature_type列是否存在
            cursor.execute(f"PRAGMA table_info(signature);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'signature_type' not in column_names:
                print("添加signature_type列到signature表...")
                cursor.execute("ALTER TABLE signature ADD COLUMN signature_type VARCHAR(50) DEFAULT 'handwriting';")
                print("signature_type列已添加")
            else:
                print("signature_type列已存在")
        else:
            print("signature表不存在，跳过")

        # 检查user_signature表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_signature';")
        if cursor.fetchone():
            print("检查user_signature表结构...")
            # 检查signature_id列是否存在
            cursor.execute(f"PRAGMA table_info(user_signature);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'signature_id' not in column_names:
                print("添加signature_id列到user_signature表...")
                cursor.execute("ALTER TABLE user_signature ADD COLUMN signature_id INTEGER;")
                print("signature_id列已添加")
            else:
                print("signature_id列已存在")
        else:
            print("user_signature表不存在，跳过")

        conn.commit()
        conn.close()
        print("数据库更新成功")
        return True
        
    except Exception as e:
        print(f"数据库操作错误: {str(e)}")
        traceback.print_exc()
        return False

def upgrade_with_flask():
    """尝试使用Flask-SQLAlchemy进行数据库升级"""
    app_module = find_app_path()
    if not app_module:
        return False
        
    try:
        if app_module == 'app':
            from app import db, create_app
            from app.models import Signature, UserSignature
        else:
            from paperless import db, create_app
            from paperless.models import Signature, UserSignature
            
        app = create_app()
        with app.app_context():
            # 检查是否有signature_type字段
            try:
                # 尝试添加signature_type字段
                db.engine.execute('ALTER TABLE signature ADD COLUMN signature_type VARCHAR(50) DEFAULT "handwriting"')
                print("添加signature_type列到signature表...")
            except Exception as e:
                print(f"注意: {e}")
                
            # 检查是否有signature_id字段
            try:
                # 尝试添加signature_id字段
                db.engine.execute('ALTER TABLE user_signature ADD COLUMN signature_id INTEGER')
                print("添加signature_id列到user_signature表...")
            except Exception as e:
                print(f"注意: {e}")
                
            db.session.commit()
            print("使用Flask-SQLAlchemy更新数据库成功")
            return True
            
    except Exception as e:
        print(f"使用Flask-SQLAlchemy更新失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数，尝试不同方式升级数据库"""
    print("开始数据库迁移...")
    
    # 首先尝试使用Flask-SQLAlchemy
    if upgrade_with_flask():
        print("使用Flask-SQLAlchemy成功完成迁移")
        return
        
    # 如果失败，尝试直接使用SQLite
    print("尝试使用直接的SQLite连接...")
    if upgrade_with_direct_sqlite():
        print("使用SQLite直接连接成功完成迁移")
        return
        
    print("所有迁移方法都失败")

if __name__ == "__main__":
    main() 