#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复PDF签名错误的脚本
"""

import sys
import traceback

def fix_user_signature_error():
    """修复UserSignature错误"""
    print("🔧 修复UserSignature错误...")
    
    try:
        # 检查models.py中的UserSignature定义
        with open('app/models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保UserSignature模型正确
        if 'class UserSignature(db.Model):' in content:
            print("  ✅ UserSignature模型定义正确")
        else:
            print("  ❌ UserSignature模型定义有问题")
            return False
            
        # 检查是否有错误的signature_id引用
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'signature_id' in line and 'UserSignature' in line:
                print(f"  ⚠️ 第{i+1}行可能有问题: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def check_file_model():
    """检查File模型是否有physical_path属性"""
    print("\n🔧 检查File模型...")
    
    try:
        with open('app/models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'physical_path' in content:
            print("  ✅ File模型包含physical_path属性")
        else:
            print("  ⚠️ File模型不包含physical_path属性")
            
        # 检查File模型的定义
        if 'class File(db.Model):' in content:
            print("  ✅ File模型定义正确")
        else:
            print("  ❌ File模型定义有问题")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def test_imports():
    """测试导入是否正常"""
    print("\n🔧 测试模型导入...")
    
    try:
        # 尝试导入模型
        sys.path.append('.')
        from app.models import User, File, UserSignature, Signature
        
        print("  ✅ User模型导入成功")
        print("  ✅ File模型导入成功")
        print("  ✅ UserSignature模型导入成功")
        print("  ✅ Signature模型导入成功")
        
        # 检查UserSignature的属性
        user_sig_attrs = dir(UserSignature)
        print(f"  📋 UserSignature属性: {[attr for attr in user_sig_attrs if not attr.startswith('_')]}")
        
        if 'signature_id' in user_sig_attrs:
            print("  ⚠️ UserSignature有signature_id属性")
        else:
            print("  ✅ UserSignature没有signature_id属性（正确）")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False

def check_routes_file():
    """检查routes.py中的问题"""
    print("\n🔧 检查routes.py...")
    
    try:
        with open('app/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找可能的问题
        lines = content.split('\n')
        issues = []
        
        for i, line in enumerate(lines):
            # 检查UserSignature.signature_id的错误使用
            if 'UserSignature' in line and 'signature_id' in line:
                issues.append(f"第{i+1}行: {line.strip()}")
            
            # 检查physical_path的使用
            if 'physical_path' in line:
                issues.append(f"第{i+1}行使用了physical_path: {line.strip()}")
        
        if issues:
            print("  ⚠️ 发现潜在问题:")
            for issue in issues:
                print(f"    {issue}")
        else:
            print("  ✅ 没有发现明显问题")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n" + "="*60)
    print("PDF签名错误修复建议")
    print("="*60)
    
    print("\n🔍 常见问题和解决方案:")
    
    print("\n1. UserSignature.signature_id错误:")
    print("   问题: 代码中试图访问UserSignature.signature_id属性")
    print("   解决: UserSignature模型使用'id'属性，不是'signature_id'")
    print("   修复: 将UserSignature.signature_id改为UserSignature.id")
    
    print("\n2. File.physical_path错误:")
    print("   问题: 代码中试图访问File.physical_path属性")
    print("   解决: File模型可能没有physical_path属性")
    print("   修复: 检查File模型定义，使用正确的属性名")
    
    print("\n3. 数据库同步问题:")
    print("   问题: 模型定义与数据库不同步")
    print("   解决: 运行数据库迁移")
    print("   命令: python quick_fix_database.py")
    
    print("\n🛠️ 立即修复步骤:")
    print("1. 检查app/models.py中的模型定义")
    print("2. 确保所有属性名称正确")
    print("3. 运行数据库更新脚本")
    print("4. 重启Flask应用")
    
    print("\n🔧 调试命令:")
    print("python -c \"from app.models import UserSignature; print(dir(UserSignature))\"")

def main():
    """主函数"""
    print("PDF签名错误修复工具")
    print("="*50)
    
    # 执行各项检查
    success1 = fix_user_signature_error()
    success2 = check_file_model()
    success3 = test_imports()
    success4 = check_routes_file()
    
    # 生成修复建议
    generate_fix_suggestions()
    
    overall_success = success1 and success2 and success3 and success4
    
    if overall_success:
        print(f"\n🎉 检查完成！")
    else:
        print(f"\n⚠️ 发现问题，请按照建议进行修复。")
    
    return overall_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n修复过程中发生错误: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        sys.exit(1)
