import os
import secrets
from datetime import datetime, timedelta

class Config:
    """
    MySQL数据库配置示例
    在Ubuntu生产环境中使用此配置
    """
    
    # 定义基础路径
    BASE_DIR = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    
    # 安全密钥 - 生产环境中请使用强随机密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    
    # MySQL数据库配置
    # 请根据实际环境修改以下配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'file_manager'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'your_password_here'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'file_manager_db'
    
    # 构建数据库URI
    SQLALCHEMY_DATABASE_URI = (
        f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@'
        f'{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}'
        f'?charset=utf8mb4'
    )
    
    # SQLAlchemy配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # 上传文件路径
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or os.path.join(BASE_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 1600 * 1024 * 1024  # 1.6GB
    
    # 服务器配置
    PORT = int(os.environ.get('PORT') or 2026)
    HOST = os.environ.get('HOST') or '0.0.0.0'  # 监听所有接口
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 日志配置
    LOG_DIR = os.environ.get('LOG_DIR') or os.path.join(BASE_DIR, 'logs')
    LOG_FILE = os.path.join(LOG_DIR, 'app.log')
    LOG_FORMAT = '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    
    # Session配置
    SESSION_TYPE = 'filesystem'
    SESSION_PERMANENT = False
    SESSION_USE_SIGNER = True
    SESSION_FILE_DIR = os.path.join(BASE_DIR, 'flask_session')
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # 文件上传安全配置
    ALLOWED_EXTENSIONS = {
        'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 
        'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', '7z', 'mp4', 
        'avi', 'mov', 'mp3', 'wav', 'bmp', 'tiff', 'svg'
    }
    
    # 确保必要的目录存在
    REQUIRED_DIRS = [
        UPLOAD_FOLDER,
        LOG_DIR,
        SESSION_FILE_DIR,
        os.path.join(BASE_DIR, 'instance'),
        os.path.join(BASE_DIR, 'backups')
    ]
    
    # 创建必要的目录
    for path in REQUIRED_DIRS:
        if path and not os.path.exists(path):
            try:
                os.makedirs(path, mode=0o755)
                print(f"创建目录: {path}")
            except Exception as e:
                print(f"创建目录失败 {path}: {str(e)}")

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ECHO = True  # 显示SQL查询

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_ECHO = False
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 日志级别
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    WTF_CSRF_ENABLED = False
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# 使用说明
"""
使用此配置文件的步骤：

1. 安装MySQL服务器：
   sudo apt update
   sudo apt install mysql-server

2. 创建数据库和用户：
   sudo mysql
   CREATE DATABASE file_manager_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'file_manager'@'localhost' IDENTIFIED BY 'your_password_here';
   GRANT ALL PRIVILEGES ON file_manager_db.* TO 'file_manager'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;

3. 设置环境变量（可选）：
   export MYSQL_PASSWORD="your_actual_password"
   export SECRET_KEY="your_secret_key"
   export FLASK_ENV="production"

4. 替换配置文件：
   cp config_mysql_example.py instance/config.py

5. 运行数据库迁移：
   flask db upgrade

6. 启动应用：
   python run.py
"""
