#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即测试PDF签名功能
"""

import requests
import sys
import time

def test_pdf_signature_access():
    """测试PDF签名页面访问"""
    print("🔍 测试PDF签名页面访问...")
    
    base_url = "http://192.168.10.77:2026"
    test_url = f"{base_url}/file/pdf_signature/860"
    
    try:
        response = requests.get(test_url, timeout=10, allow_redirects=False)
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ PDF签名页面访问成功！")
            
            # 检查页面内容
            content = response.text
            if 'PDF手写签名' in content:
                print("  ✅ 页面内容正确")
            else:
                print("  ⚠️ 页面内容可能不完整")
            
            return True
            
        elif response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"  ⚠️ 页面重定向到: {location}")
            
            if 'login' in location:
                print("  💡 需要登录，这是正常的")
                return True
            else:
                print("  ⚠️ 重定向到其他页面")
                return False
        else:
            print(f"  ❌ 访问失败 - 状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_server_logs():
    """检查服务器日志"""
    print("\n📋 检查服务器状态...")
    
    base_url = "http://192.168.10.77:2026"
    
    try:
        # 测试主页
        response = requests.get(base_url, timeout=5)
        if response.status_code in [200, 302]:
            print("  ✅ 服务器运行正常")
            return True
        else:
            print(f"  ⚠️ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 服务器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("PDF签名功能立即测试")
    print("="*40)
    
    # 测试服务器
    server_ok = test_server_logs()
    
    if not server_ok:
        print("\n❌ 服务器未运行，请先启动：python run.py")
        return False
    
    # 测试PDF签名页面
    signature_ok = test_pdf_signature_access()
    
    if signature_ok:
        print("\n🎉 测试成功！")
        print("\n📋 下一步操作:")
        print("1. 在浏览器中访问: http://192.168.10.77:2026")
        print("2. 登录系统")
        print("3. 找到PDF文件并点击进入预览")
        print("4. 点击'PDF手写签名'按钮")
        print("5. 测试签名功能")
        
        print("\n🎯 预期功能:")
        print("✅ PDF文档自动全部展开显示")
        print("✅ 可以添加新签名")
        print("✅ 签名可以拖拽移动")
        print("✅ 签名可以编辑和删除")
        print("✅ 位置自动保存")
        
        return True
    else:
        print("\n❌ PDF签名页面访问失败")
        print("请检查应用日志查看具体错误")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
