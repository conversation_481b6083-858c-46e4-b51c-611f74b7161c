#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL问题修复脚本
修复PDF手写签名功能中的URL引用问题
"""

import os
import re
import sys

def fix_file(file_path, replacements):
    """修复单个文件中的URL问题"""
    if not os.path.exists(file_path):
        print(f"⚠️ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 应用替换
        for old_pattern, new_pattern in replacements:
            if old_pattern in content:
                content = content.replace(old_pattern, new_pattern)
                changes_made += 1
                print(f"  ✅ 替换: {old_pattern} -> {new_pattern}")
        
        # 如果有更改，写回文件
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {file_path} 修复完成，共 {changes_made} 处更改")
            return True
        else:
            print(f"✅ {file_path} 无需修复")
            return True
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 失败: {e}")
        return False

def main():
    """主修复函数"""
    print("PDF手写签名功能 - URL问题修复工具")
    print("=" * 50)
    
    # 定义需要修复的文件和替换规则
    fixes = [
        {
            'file': 'app/routes.py',
            'replacements': [
                ("url_for('main.file_detail'", "url_for('main.preview_file'"),
            ]
        },
        {
            'file': 'app/templates/preview/pdf_sign.html',
            'replacements': [
                ("url_for('main.file_detail'", "url_for('main.preview_file'"),
                ("url_for('main.file_preview'", "url_for('main.stream_file'"),
            ]
        }
    ]
    
    success_count = 0
    total_count = len(fixes)
    
    for fix in fixes:
        file_path = fix['file']
        replacements = fix['replacements']
        
        print(f"\n修复文件: {file_path}")
        
        if fix_file(file_path, replacements):
            success_count += 1
    
    # 检查其他可能的问题文件
    print(f"\n检查其他可能的问题...")
    
    # 搜索所有可能包含file_detail的文件
    search_patterns = [
        "app/templates/**/*.html",
        "app/*.py"
    ]
    
    problem_files = []
    
    # 简单的文件搜索
    for root, dirs, files in os.walk('app'):
        for file in files:
            if file.endswith(('.py', '.html')):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'file_detail' in content and 'url_for' in content:
                            problem_files.append(file_path)
                except:
                    pass
    
    if problem_files:
        print(f"\n发现可能包含file_detail引用的文件:")
        for file_path in problem_files:
            print(f"  ⚠️ {file_path}")
        print("\n请手动检查这些文件并修复URL引用")
    else:
        print("✅ 未发现其他问题文件")
    
    # 总结
    print(f"\n{'='*50}")
    print(f"修复结果: {success_count}/{total_count} 个文件修复成功")
    
    if success_count == total_count:
        print("🎉 URL问题修复完成！")
        print("\n下一步:")
        print("1. 重启Flask应用")
        print("2. 测试PDF手写签名功能")
        print("3. 运行: python test_routes.py")
        return True
    else:
        print("❌ 部分文件修复失败")
        return False

def verify_routes():
    """验证路由定义"""
    print("\n验证路由定义...")
    
    routes_file = 'app/routes.py'
    if not os.path.exists(routes_file):
        print(f"❌ 路由文件不存在: {routes_file}")
        return False
    
    try:
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键路由是否存在
        required_routes = [
            'def preview_file',
            'def pdf_handwriting_signature',
            'def save_pdf_signature',
            'def delete_pdf_signature'
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in content:
                missing_routes.append(route)
            else:
                print(f"✅ 找到路由: {route}")
        
        if missing_routes:
            print(f"❌ 缺少路由: {missing_routes}")
            return False
        else:
            print("✅ 所有必需路由都存在")
            return True
            
    except Exception as e:
        print(f"❌ 验证路由失败: {e}")
        return False

if __name__ == "__main__":
    try:
        # 修复URL问题
        fix_success = main()
        
        # 验证路由
        route_success = verify_routes()
        
        overall_success = fix_success and route_success
        
        if overall_success:
            print("\n🎉 所有问题修复完成！")
            print("现在可以重启Flask应用并测试PDF手写签名功能了。")
        else:
            print("\n❌ 修复过程中遇到问题，请检查上述错误信息。")
        
        sys.exit(0 if overall_success else 1)
        
    except KeyboardInterrupt:
        print("\n\n修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n修复过程中发生错误: {e}")
        sys.exit(1)
