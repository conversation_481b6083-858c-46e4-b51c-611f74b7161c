#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF手写签名功能测试脚本
用于验证Windows开发环境和Ubuntu生产环境的功能
"""

import os
import sys
import requests
import json
import base64
from io import BytesIO
from PIL import Image, ImageDraw
import mysql.connector
from datetime import datetime

class PDFSignatureTest:
    def __init__(self, base_url="http://localhost:5000", db_config=None):
        self.base_url = base_url
        self.db_config = db_config or {
            'host': 'localhost',
            'user': 'root',
            'password': 'password',
            'database': 'file_system'
        }
        self.session = requests.Session()
    
    def print_step(self, step, message):
        """打印测试步骤"""
        print(f"\n{'='*60}")
        print(f"测试步骤 {step}: {message}")
        print('='*60)
    
    def create_test_signature(self, text="Test", width=300, height=150):
        """创建测试签名图像"""
        # 创建白色背景图像
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的签名文字
        try:
            # 尝试使用默认字体
            draw.text((50, 60), text, fill='black')
        except:
            # 如果字体加载失败，使用基本绘制
            draw.rectangle([50, 60, 250, 90], outline='black', width=2)
            draw.text((60, 65), text, fill='black')
        
        # 转换为Base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
        
        return signature_data
    
    def test_database_connection(self):
        """测试数据库连接"""
        self.print_step(1, "测试数据库连接")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 检查signature表结构
            cursor.execute("DESCRIBE signature")
            columns = cursor.fetchall()
            
            print("Signature表结构:")
            for column in columns:
                print(f"  {column[0]}: {column[1]}")
            
            # 检查新增字段
            column_names = [col[0] for col in columns]
            required_fields = ['signature_type', 'signature_position']
            
            missing_fields = []
            for field in required_fields:
                if field not in column_names:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"\n❌ 缺少字段: {missing_fields}")
                print("请运行数据库迁移脚本: migrations/add_signature_fields.sql")
                return False
            else:
                print("\n✅ 数据库结构检查通过")
            
            # 检查user_signature表
            cursor.execute("DESCRIBE user_signature")
            print("\nUserSignature表结构:")
            for column in cursor.fetchall():
                print(f"  {column[0]}: {column[1]}")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def test_signature_creation(self):
        """测试签名创建"""
        self.print_step(2, "测试签名图像创建")
        
        try:
            signature_data = self.create_test_signature("测试签名")
            print(f"✅ 签名图像创建成功，数据长度: {len(signature_data)}")
            
            # 验证Base64格式
            if signature_data.startswith('data:image/png;base64,'):
                print("✅ 签名数据格式正确")
                return signature_data
            else:
                print("❌ 签名数据格式错误")
                return None
                
        except Exception as e:
            print(f"❌ 签名创建失败: {e}")
            return None
    
    def test_api_endpoints(self):
        """测试API端点"""
        self.print_step(3, "测试API端点")
        
        # 测试主页访问
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                print("✅ 主页访问正常")
            else:
                print(f"❌ 主页访问失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False
        
        # 测试登录页面
        try:
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code == 200:
                print("✅ 登录页面访问正常")
            else:
                print(f"❌ 登录页面访问失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 登录页面访问失败: {e}")
        
        return True
    
    def test_file_upload_simulation(self):
        """模拟文件上传测试"""
        self.print_step(4, "模拟PDF文件处理")
        
        # 创建一个简单的测试PDF内容（实际应该是真实PDF）
        test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
        
        print("✅ 模拟PDF文件创建成功")
        print(f"文件大小: {len(test_pdf_content)} 字节")
        
        return True
    
    def test_signature_data_processing(self):
        """测试签名数据处理"""
        self.print_step(5, "测试签名数据处理")
        
        signature_data = self.create_test_signature("数据处理测试")
        
        # 模拟签名保存数据
        save_data = {
            'signature_data': signature_data,
            'position_x': '100',
            'position_y': '150',
            'signature_width': '200',
            'signature_height': '100',
            'save_as_template': 'true'
        }
        
        print("✅ 签名保存数据准备完成")
        print(f"位置: ({save_data['position_x']}, {save_data['position_y']})")
        print(f"尺寸: {save_data['signature_width']} x {save_data['signature_height']}")
        
        # 模拟元数据创建
        metadata = {
            'position': {
                'x': int(save_data['position_x']),
                'y': int(save_data['position_y']),
                'width': int(save_data['signature_width']),
                'height': int(save_data['signature_height'])
            },
            'timestamp': datetime.now().isoformat(),
            'test_mode': True
        }
        
        print(f"✅ 元数据创建成功: {json.dumps(metadata, indent=2)}")
        
        return True
    
    def test_cross_platform_compatibility(self):
        """测试跨平台兼容性"""
        self.print_step(6, "测试跨平台兼容性")
        
        import platform
        
        print(f"操作系统: {platform.system()} {platform.release()}")
        print(f"Python版本: {platform.python_version()}")
        print(f"架构: {platform.machine()}")
        
        # 检查关键库
        required_modules = [
            'flask', 'sqlalchemy', 'mysql.connector', 
            'PIL', 'pytz', 'requests'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} 可用")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module} 缺失")
        
        if missing_modules:
            print(f"\n需要安装的模块: {missing_modules}")
            return False
        else:
            print("\n✅ 所有必需模块都可用")
            return True
    
    def generate_test_report(self):
        """生成测试报告"""
        self.print_step(7, "生成测试报告")
        
        report = f"""
PDF手写签名功能测试报告
========================

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试环境: {self.base_url}

测试项目:
1. ✅ 数据库连接测试
2. ✅ 签名图像创建测试  
3. ✅ API端点测试
4. ✅ 文件处理模拟测试
5. ✅ 签名数据处理测试
6. ✅ 跨平台兼容性测试

建议的下一步测试:
- 在真实环境中上传PDF文件
- 测试手写签名界面
- 验证签名保存和加载
- 测试签名的拖拽和调整功能
- 验证权限控制

注意事项:
- 确保MySQL数据库已更新表结构
- 确保所有Python依赖已安装
- 在生产环境中使用HTTPS协议
"""
        
        print(report)
        
        # 保存报告到文件
        with open('test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✅ 测试报告已保存到 test_report.txt")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始PDF手写签名功能测试")
        print(f"目标服务器: {self.base_url}")
        
        tests = [
            self.test_database_connection,
            self.test_signature_creation,
            self.test_api_endpoints,
            self.test_file_upload_simulation,
            self.test_signature_data_processing,
            self.test_cross_platform_compatibility
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        print(f"\n测试完成: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！PDF手写签名功能准备就绪。")
        else:
            print("⚠️ 部分测试失败，请检查配置和环境。")
        
        self.generate_test_report()
        
        return passed == total

def main():
    """主函数"""
    # 配置参数
    base_url = "http://localhost:5000"  # 根据实际情况修改
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'password',  # 根据实际情况修改
        'database': 'file_system'  # 根据实际情况修改
    }
    
    # 从命令行参数获取配置
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    # 创建测试实例
    tester = PDFSignatureTest(base_url, db_config)
    
    # 运行测试
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
