#!/bin/bash

# 电子签名功能部署脚本
# 用于在Ubuntu服务器上部署新的电子签名功能

set -e  # 遇到错误立即退出

echo "=========================================="
echo "电子签名功能部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统版本"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_warning "此脚本专为Ubuntu设计，当前系统：$ID"
    fi
    
    log_success "系统环境检查完成"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $PYTHON_VERSION"
    
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装"
        exit 1
    fi
    
    log_success "Python环境检查完成"
}

# 检查项目目录
check_project() {
    log_info "检查项目目录..."
    
    if [[ ! -f "app/__init__.py" ]]; then
        log_error "当前目录不是有效的Flask项目目录"
        exit 1
    fi
    
    if [[ ! -f "requirements.txt" ]]; then
        log_error "未找到requirements.txt文件"
        exit 1
    fi
    
    log_success "项目目录检查完成"
}

# 备份现有文件
backup_files() {
    log_info "备份现有文件..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)_signature_deploy"
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    if [[ -f "app/routes.py" ]]; then
        cp "app/routes.py" "$BACKUP_DIR/routes.py.bak"
        log_info "已备份 routes.py"
    fi
    
    if [[ -f "app/templates/preview/pdf_sign.html" ]]; then
        cp "app/templates/preview/pdf_sign.html" "$BACKUP_DIR/pdf_sign.html.bak"
        log_info "已备份 pdf_sign.html"
    fi
    
    if [[ -f "instance/file_manager.db" ]]; then
        cp "instance/file_manager.db" "$BACKUP_DIR/file_manager.db.bak"
        log_info "已备份数据库文件"
    fi
    
    log_success "文件备份完成，备份目录：$BACKUP_DIR"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    # 创建虚拟环境（如果不存在）
    if [[ ! -d "venv" ]]; then
        log_info "创建虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    pip install -r requirements.txt
    
    log_success "依赖安装完成"
}

# 检查数据库
check_database() {
    log_info "检查数据库结构..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 运行数据库检查脚本
    python3 -c "
from app import create_app, db
from app.models import User, Signature, UserSignature

app = create_app()
with app.app_context():
    try:
        # 检查表是否存在
        db.engine.execute('SELECT 1 FROM user LIMIT 1')
        print('✓ user表存在')
        
        db.engine.execute('SELECT 1 FROM signature LIMIT 1')
        print('✓ signature表存在')
        
        db.engine.execute('SELECT 1 FROM user_signature LIMIT 1')
        print('✓ user_signature表存在')
        
        print('数据库结构检查完成')
    except Exception as e:
        print(f'数据库检查失败: {e}')
        exit(1)
"
    
    if [[ $? -eq 0 ]]; then
        log_success "数据库结构检查完成"
    else
        log_error "数据库结构检查失败"
        exit 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查是否需要迁移
    if [[ -d "migrations" ]]; then
        # 运行迁移
        flask db upgrade
        log_success "数据库迁移完成"
    else
        log_warning "未找到migrations目录，跳过数据库迁移"
    fi
}

# 测试功能
test_functionality() {
    log_info "测试电子签名功能..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 运行测试脚本
    if [[ -f "test_signature_functionality.py" ]]; then
        python3 test_signature_functionality.py
        if [[ $? -eq 0 ]]; then
            log_success "功能测试通过"
        else
            log_warning "功能测试未完全通过，请检查日志"
        fi
    else
        log_warning "未找到测试脚本，跳过功能测试"
    fi
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    
    # 检查是否有运行的进程
    if pgrep -f "python.*run.py" > /dev/null; then
        log_info "停止现有进程..."
        pkill -f "python.*run.py"
        sleep 2
    fi
    
    # 检查是否使用systemd服务
    if systemctl is-active --quiet file-manager; then
        log_info "重启systemd服务..."
        sudo systemctl restart file-manager
        log_success "服务重启完成"
    else
        log_info "手动启动应用..."
        # 激活虚拟环境
        source venv/bin/activate
        nohup python3 run.py > logs/app.log 2>&1 &
        log_success "应用已启动"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 等待服务启动
    sleep 5
    
    # 检查端口是否监听
    if netstat -tuln | grep -q ":2026"; then
        log_success "应用正在监听端口2026"
    else
        log_warning "应用可能未正常启动，请检查日志"
    fi
    
    # 检查日志文件
    if [[ -f "logs/app.log" ]]; then
        log_info "最新日志："
        tail -n 5 logs/app.log
    fi
}

# 显示部署结果
show_results() {
    echo
    echo "=========================================="
    echo "部署完成"
    echo "=========================================="
    echo
    echo "📋 部署摘要："
    echo "  • 电子签名功能已更新"
    echo "  • 数据库结构已检查"
    echo "  • 应用服务已重启"
    echo
    echo "🌐 访问地址："
    echo "  • 主页: http://localhost:2026"
    echo "  • 签名管理: http://localhost:2026/user/signatures"
    echo
    echo "📁 重要文件："
    echo "  • 日志文件: logs/app.log"
    echo "  • 配置文件: instance/config.py"
    echo "  • 备份目录: $BACKUP_DIR"
    echo
    echo "🔧 后续操作："
    echo "  1. 测试电子签名功能"
    echo "  2. 检查用户权限设置"
    echo "  3. 监控系统日志"
    echo
    log_success "部署脚本执行完成！"
}

# 主函数
main() {
    log_info "开始部署电子签名功能..."
    
    check_root
    check_environment
    check_python
    check_project
    backup_files
    install_dependencies
    check_database
    run_migrations
    test_functionality
    restart_service
    verify_deployment
    show_results
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
