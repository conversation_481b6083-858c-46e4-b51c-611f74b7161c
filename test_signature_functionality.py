#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电子签名功能测试脚本
测试新的电子签名功能是否正常工作
"""

import os
import sys
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_signature_functionality():
    """测试电子签名功能"""
    
    print("=" * 60)
    print("电子签名功能测试")
    print("=" * 60)
    
    # 测试配置
    base_url = "http://localhost:2026"
    test_user = {
        "username": "test_user",
        "password": "test_password"
    }
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 测试登录
        print("\n1. 测试用户登录...")
        login_data = {
            "username": test_user["username"],
            "password": test_user["password"]
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            print("✓ 登录成功")
        else:
            print(f"✗ 登录失败: {response.status_code}")
            return False
        
        # 2. 测试访问PDF签名页面
        print("\n2. 测试访问PDF签名页面...")
        # 假设有一个PDF文件ID为1
        pdf_sign_url = f"{base_url}/file/pdf_signature/1"
        response = session.get(pdf_sign_url)
        
        if response.status_code == 200:
            print("✓ PDF签名页面访问成功")
            
            # 检查页面是否包含必要的元素
            content = response.text
            required_elements = [
                'id="btnNewSignature"',
                'id="selectSignature"',
                'id="signatureModal"',
                'id="signaturePad"',
                'class="signature-controls"'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"✗ 页面缺少必要元素: {missing_elements}")
            else:
                print("✓ 页面包含所有必要的签名元素")
                
        else:
            print(f"✗ PDF签名页面访问失败: {response.status_code}")
        
        # 3. 测试用户签名管理页面
        print("\n3. 测试用户签名管理页面...")
        user_signatures_url = f"{base_url}/user/signatures"
        response = session.get(user_signatures_url)
        
        if response.status_code == 200:
            print("✓ 用户签名管理页面访问成功")
        else:
            print(f"✗ 用户签名管理页面访问失败: {response.status_code}")
        
        # 4. 测试添加用户签名API
        print("\n4. 测试添加用户签名API...")
        
        # 模拟签名数据（Base64编码的图片）
        mock_signature_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        signature_data = {
            "signature_data": mock_signature_data,
            "description": "测试签名",
            "set_as_default": "true"
        }
        
        add_signature_url = f"{base_url}/user/add_signature"
        response = session.post(add_signature_url, data=signature_data)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✓ 添加用户签名成功")
                    signature_id = result.get('signature_id')
                    print(f"  签名ID: {signature_id}")
                else:
                    print(f"✗ 添加用户签名失败: {result.get('message')}")
            except json.JSONDecodeError:
                print("✗ 添加用户签名响应格式错误")
        else:
            print(f"✗ 添加用户签名请求失败: {response.status_code}")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {str(e)}")
        return False
    
    finally:
        # 清理会话
        session.close()

def check_database_structure():
    """检查数据库结构是否正确"""
    
    print("\n检查数据库结构...")
    
    try:
        from app import create_app, db
        from app.models import User, Signature, UserSignature
        
        app = create_app()
        with app.app_context():
            # 检查表是否存在
            tables_to_check = ['user', 'signature', 'user_signature']
            
            for table_name in tables_to_check:
                try:
                    result = db.engine.execute(f"SELECT 1 FROM {table_name} LIMIT 1")
                    print(f"✓ 表 '{table_name}' 存在")
                except Exception as e:
                    print(f"✗ 表 '{table_name}' 不存在或有问题: {str(e)}")
            
            # 检查UserSignature表的字段
            try:
                user_signature = UserSignature.query.first()
                print("✓ UserSignature模型可以正常查询")
            except Exception as e:
                print(f"✗ UserSignature模型查询失败: {str(e)}")
                
    except Exception as e:
        print(f"✗ 数据库结构检查失败: {str(e)}")

def main():
    """主函数"""
    
    print("开始电子签名功能测试...")
    
    # 检查数据库结构
    check_database_structure()
    
    # 测试功能
    success = test_signature_functionality()
    
    if success:
        print("\n🎉 所有测试通过！电子签名功能已准备就绪。")
    else:
        print("\n❌ 部分测试失败，请检查配置和代码。")

if __name__ == "__main__":
    main()
