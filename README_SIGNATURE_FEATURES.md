# 电子签名功能说明

## 功能概述

本系统提供了完整的电子签名功能，支持在PDF文档上添加、管理和验证电子签名。

## 主要功能

### 1. 添加新签名

**操作步骤：**
1. 打开PDF文档
2. 点击工具栏中的"新建签名"按钮
3. 点击PDF文档上您想要添加签名的任意位置
4. 在弹出的模态框中用鼠标手写签名
5. 点击"确认签名"按钮完成签名

**特点：**
- 支持自定义笔迹颜色和粗细
- 实时预览签名效果
- 可以清除重新签名

### 2. 使用已保存的签名

**操作步骤：**
1. 在"使用保存的签名"下拉框中选择已有签名
2. 点击PDF文档上您想要放置签名的位置
3. 签名会自动放置到指定位置

**特点：**
- 快速重复使用常用签名
- 支持多个预设签名
- 一键放置，提高效率

### 3. 移动签名

**操作方法：**
- 直接用鼠标拖动签名到需要的位置
- 支持实时位置调整
- 自动限制在PDF文档范围内

### 4. 调整签名大小

**控制按钮：**
- **绿色"+"按钮**：放大签名（最大400px）
- **蓝色"-"按钮**：缩小签名（最小50px）
- 每次调整20%的大小
- 显示临时提示当前尺寸

### 5. 修改签名

**操作步骤：**
1. 点击签名上方的**黄色"修改"按钮**
2. 在弹出的模态框中重新签名
3. 点击"确认签名"完成修改

**特点：**
- 保持原有位置和大小
- 只更新签名内容
- 支持完全重新绘制

### 6. 删除签名

**操作方法：**
- 点击签名上方的**红色"删除"按钮**
- 确认删除操作
- 立即从文档中移除

## 签名控制栏

每个签名都有一个控制栏，包含以下按钮：

| 按钮 | 颜色 | 图标 | 功能 |
|------|------|------|------|
| 修改 | 黄色 | 🖊️ | 重新签名 |
| 放大 | 绿色 | ➕ | 增大签名 |
| 缩小 | 蓝色 | ➖ | 减小签名 |
| 删除 | 红色 | 🗑️ | 删除签名 |

## 用户签名管理

### 访问路径
- 主菜单 → 个人签名管理
- 或直接访问：`/user/signatures`

### 功能特点
1. **添加新签名**：创建可重复使用的签名模板
2. **设置默认签名**：标记常用签名
3. **签名描述**：为每个签名添加说明
4. **删除签名**：移除不需要的签名

## 技术特性

### 前端技术
- **SignaturePad.js**：高质量手写签名捕获
- **HTML5 Canvas**：流畅的绘图体验
- **Bootstrap 5**：响应式界面设计
- **Font Awesome**：丰富的图标库

### 后端技术
- **Flask**：Web框架
- **SQLAlchemy**：数据库ORM
- **Base64编码**：签名数据存储
- **MySQL支持**：生产环境数据库

### 数据库结构

#### Signature表（签名记录）
```sql
CREATE TABLE signature (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    file_id INTEGER NOT NULL,
    signature_data TEXT NOT NULL,
    signature_date DATETIME,
    signature_metadata TEXT,
    signature_type VARCHAR(50),
    signature_position VARCHAR(100)
);
```

#### UserSignature表（用户签名模板）
```sql
CREATE TABLE user_signature (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    signature_data TEXT NOT NULL,
    created_at DATETIME,
    is_default BOOLEAN,
    description TEXT
);
```

## 安全特性

1. **用户权限验证**：只有授权用户可以签名
2. **文件访问控制**：基于文件夹权限
3. **签名完整性**：Base64编码保证数据完整
4. **操作日志**：记录所有签名操作
5. **时间戳**：每个签名都有创建时间

## 浏览器兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ⚠️ IE 11（部分功能受限）

## 使用建议

### 最佳实践
1. **签名尺寸**：建议保持在100-200px宽度
2. **签名位置**：避免放置在文档重要内容上
3. **签名管理**：定期清理不需要的签名模板
4. **权限控制**：合理设置文件夹访问权限

### 性能优化
1. **签名数量**：单个文档建议不超过10个签名
2. **文件大小**：大型PDF可能影响加载速度
3. **网络环境**：稳定网络确保签名保存成功

## 故障排除

### 常见问题

**Q: 签名板无法绘制？**
A: 检查浏览器是否支持HTML5 Canvas，尝试刷新页面。

**Q: 签名保存失败？**
A: 检查网络连接和用户权限，确保有足够的存储空间。

**Q: 无法看到已保存的签名？**
A: 检查数据库连接，确保UserSignature表结构正确。

**Q: 签名位置不准确？**
A: 确保PDF容器完全加载，避免在加载过程中操作。

### 调试工具
- 浏览器开发者工具控制台
- 服务器日志文件：`logs/app.log`
- 数据库查询工具

## 更新日志

### v2.0.0 (2024-01-XX)
- ✨ 新增：点击PDF任意位置添加签名
- ✨ 新增：使用已保存签名快速放置
- ✨ 新增：拖拽移动签名位置
- ✨ 新增：签名大小调整功能
- ✨ 新增：签名修改功能
- 🎨 改进：签名控制按钮UI设计
- 🐛 修复：签名数据获取逻辑
- 🔧 优化：用户体验和交互流程

### v1.0.0 (2024-01-XX)
- 🎉 初始版本发布
- ✨ 基础签名功能
- ✨ 用户签名管理
- ✨ 签名验证功能

## 联系支持

如有问题或建议，请联系开发团队：
- 📧 Email: <EMAIL>
- 📱 电话: +86-xxx-xxxx-xxxx
- 💬 在线客服: 工作日 9:00-18:00
