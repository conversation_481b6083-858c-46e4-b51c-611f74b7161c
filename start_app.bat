@echo off
REM 快速启动应用脚本

echo ==========================================
echo 启动文件管理系统 (电子签名版本)
echo ==========================================

REM 检查虚拟环境
if not exist "venv" (
    echo [ERROR] 虚拟环境不存在，请先运行 deploy_signature_features.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 检查必要目录
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "instance" mkdir instance

REM 设置环境变量
set FLASK_APP=run.py
set FLASK_ENV=development

echo [INFO] 启动应用...
echo [INFO] 访问地址: http://localhost:2026
echo [INFO] 按 Ctrl+C 停止应用
echo.

REM 启动应用
python run.py

REM 停用虚拟环境
deactivate

echo.
echo 应用已停止
pause
