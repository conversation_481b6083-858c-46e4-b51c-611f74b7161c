#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复PDF签名错误
"""

import sys
import os

def fix_signature_template_issue():
    """修复签名模板中的问题"""
    print("🔧 修复签名模板问题...")
    
    try:
        # 检查模板中是否有问题
        template_path = 'app/templates/preview/pdf_sign.html'
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有错误的属性引用
        issues_found = []
        
        if 'physical_path' in content:
            issues_found.append('physical_path')
        
        if 'signature_id' in content and 'UserSignature' in content:
            issues_found.append('UserSignature.signature_id')
        
        if issues_found:
            print(f"  ⚠️ 发现问题: {issues_found}")
            return False
        else:
            print("  ✅ 模板文件正常")
            return True
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def check_signature_position_parsing():
    """检查签名位置解析"""
    print("\n🔧 检查签名位置解析...")
    
    try:
        # 检查模板中的位置解析代码
        template_path = 'app/templates/preview/pdf_sign.html'
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找位置相关的代码
        if 'signature.position.x' in content:
            print("  ✅ 找到位置X引用")
        else:
            print("  ⚠️ 未找到位置X引用")
        
        if 'signature.position.y' in content:
            print("  ✅ 找到位置Y引用")
        else:
            print("  ⚠️ 未找到位置Y引用")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def create_simple_test_route():
    """创建简单的测试路由"""
    print("\n🔧 创建简单测试路由...")
    
    test_route = '''
# 简单PDF签名测试路由
@main.route('/test_pdf_signature/<int:file_id>')
@login_required
def test_pdf_signature(file_id):
    """测试PDF签名功能"""
    try:
        file = File.query.get_or_404(file_id)
        
        # 简单的权限检查
        if not file.can_access(current_user):
            flash('您没有权限访问此文件', 'error')
            return redirect(url_for('main.index'))
        
        # 检查文件类型
        if not file.original_filename.lower().endswith('.pdf'):
            flash('此功能仅支持PDF文件', 'error')
            return redirect(url_for('main.preview_file', file_id=file_id))
        
        # 获取签名（简化版）
        signatures = []
        saved_signatures = []
        
        return render_template('preview/pdf_sign.html',
                              file=file,
                              signatures=signatures,
                              saved_signatures=saved_signatures)
                              
    except Exception as e:
        current_app.logger.error(f'测试PDF签名失败: {str(e)}')
        flash('访问PDF签名页面失败', 'error')
        return redirect(url_for('main.index'))
'''
    
    print("  📝 测试路由代码已准备")
    print("  💡 可以将此代码添加到routes.py中进行测试")
    
    return True

def generate_debug_info():
    """生成调试信息"""
    print("\n" + "="*60)
    print("PDF签名错误调试信息")
    print("="*60)
    
    print("\n🔍 错误分析:")
    print("1. 'File' object has no attribute 'physical_path'")
    print("   - 可能原因: 代码中引用了不存在的File.physical_path属性")
    print("   - File模型只有'path'属性，没有'physical_path'")
    print("   - 解决方案: 将physical_path改为path")
    
    print("\n2. type object 'UserSignature' has no attribute 'signature_id'")
    print("   - 可能原因: 代码中错误引用了UserSignature.signature_id")
    print("   - UserSignature模型使用'id'属性，不是'signature_id'")
    print("   - 解决方案: 将signature_id改为id")
    
    print("\n🛠️ 修复步骤:")
    print("1. 检查所有模板文件中的属性引用")
    print("2. 确保使用正确的模型属性名")
    print("3. 清除浏览器缓存")
    print("4. 重启Flask应用")
    
    print("\n🔧 临时解决方案:")
    print("1. 使用简化的路由进行测试")
    print("2. 逐步添加功能直到找到问题所在")
    print("3. 检查数据库中的数据是否正确")
    
    print("\n📋 检查清单:")
    print("□ File模型是否有path属性")
    print("□ UserSignature模型是否有id属性")
    print("□ 模板中是否正确引用属性")
    print("□ 数据库是否已正确迁移")

def main():
    """主函数"""
    print("PDF签名错误快速修复工具")
    print("="*50)
    
    # 执行检查
    success1 = fix_signature_template_issue()
    success2 = check_signature_position_parsing()
    success3 = create_simple_test_route()
    
    # 生成调试信息
    generate_debug_info()
    
    print(f"\n🎯 建议的修复步骤:")
    print("1. 检查File模型的属性名称")
    print("2. 确保模板中使用正确的属性")
    print("3. 清除浏览器缓存并重启应用")
    print("4. 如果问题持续，使用简化版本进行测试")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n修复过程中发生错误: {e}")
        sys.exit(1)
