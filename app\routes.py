from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, send_file, Response, session, abort, make_response, send_from_directory
from flask_login import login_required, login_user, logout_user, current_user
from werkzeug.utils import secure_filename
from app.models import User, File, Folder, UserLog, Keyword, UserGroup, Signature, UserSignature
from app import db
from app.utils import log_user_action, generate_captcha, get_text, is_super_admin, is_owner, can_access_feature
import os
import base64
from markupsafe import Markup
from datetime import datetime, timedelta
import pytz
import tempfile
import re
from werkzeug.urls import url_parse
import zipfile
import io
from sqlalchemy import or_
import time
import traceback
import json

main = Blueprint('main', __name__)
auth = Blueprint('auth', __name__)

# 添加上下文处理器，使模板能够使用翻译函数
@main.context_processor
@auth.context_processor
def inject_utils():
    return dict(
        t=get_text,  # 添加翻译函数到模板上下文
        current_language=lambda: session.get('language', 'zh'),  # 添加获取当前语言的函数
        is_super_admin=is_super_admin  # 添加超级管理员检查函数
    )

# 添加自定义过滤器
@main.app_template_filter('truncate_filename')
def truncate_filename(filename, length=35):
    """截断文件名，保留扩展名"""
    if len(filename) <= length:
        return filename
        
    name, ext = os.path.splitext(filename)
    # 保留扩展名，并留出 "..." 的空间
    max_length = length - len(ext) - 3
    if max_length < 1:
        return filename  # 如果长度太短，返回原文件名
        
    return name[:max_length] + "..." + ext

# 添加自定义时间格式化过滤器
@main.app_template_filter('format_datetime')
def format_datetime(value):
    """
    格式化时间为中国时间格式
    这里需要特别注意:
    1. 数据库中存储的是北京时间，但没有时区信息(naive datetime)
    2. 直接格式化输出，不需要进行时区转换
    """
    if value is None:
        return ''
        
    # 格式化为中国时间格式
    return value.strftime('%Y年%m月%d日 %H:%M:%S')

# 添加自定义文件大小格式化过滤器
@main.app_template_filter('format_size')
def format_size(size):
    """格式化文件大小"""
    if not size:
        return '-'
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(size)
    unit_index = 0
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    return f"{size:.2f} {units[unit_index]}"

# 认证路由
@auth.route('/captcha')
def captcha():
    """生成并返回验证码图片"""
    try:
        # 生成验证码，使用更大的尺寸
        captcha_text, captcha_image = generate_captcha(width=150, height=50)
        
        # 将验证码文本存储到session中
        session['captcha_text'] = captcha_text.lower()  # 存储为小写以便验证时不区分大小写
        session.modified = True  # 确保session被保存
        
        current_app.logger.info(f'生成验证码: {captcha_text}')
        
        # 返回图片
        return Response(
            headers={
                'Content-Type': 'image/png',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            response=base64.b64decode(captcha_image)
        )
    except Exception as e:
        current_app.logger.error(f'生成验证码失败: {str(e)}')
        # 在验证码生成失败时返回一个简单的默认验证码
        return Response(
            headers={'Content-Type': 'text/plain'},
            response="验证码生成失败，请刷新重试"
        )

@auth.route('/login', methods=['GET', 'POST'])
def login():
    # 如果用户已登录，直接跳转到主页
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        captcha_input = request.form.get('captcha', '').lower()  # 获取用户输入的验证码，转为小写
        
        # 检查输入是否完整
        if not username or not password or not captcha_input:
            flash('请输入用户名、密码和验证码')
            return render_template('login.html')
        
        # 验证码验证
        stored_captcha = session.get('captcha_text', '')
        current_app.logger.info(f'验证码比对: 输入={captcha_input}, 存储={stored_captcha}')
        
        if not stored_captcha or captcha_input != stored_captcha:
            flash('验证码错误')
            # 生成新的验证码
            captcha_text, _ = generate_captcha()
            session['captcha_text'] = captcha_text.lower()
            session.modified = True
            return render_template('login.html')
            
        # 验证用户名和密码
        user = User.query.filter_by(username=username).first()
        
        if user is None or not user.check_password(password):
            flash('用户名或密码错误')
            # 生成新的验证码
            captcha_text, _ = generate_captcha()
            session['captcha_text'] = captcha_text.lower()
            session.modified = True
            return render_template('login.html')
        
        # 特殊处理cv24051用户 - 需要回答安全问题
        if user.username == 'cv24051':
            # 检查是否已设置安全问题
            if user.security_question and user.security_answer_hash:
                # 保存验证状态到session
                session['pre_login_user_id'] = user.id
                session['pre_login_username'] = user.username
                session.modified = True
                
                # 重定向到安全问题验证页面
                return redirect(url_for('auth.security_question', user_id=user.id))
            else:
                current_app.logger.warning('cv24051用户未设置安全问题，跳过安全验证')
            
        # 登录成功，清除验证码
        session.pop('captcha_text', None)
        session.pop('pre_login_user_id', None)
        session.pop('pre_login_username', None)
        login_user(user)
        current_app.logger.info(f'用户 {username} 登录成功')
        
        # 记录登录日志
        log_user_action(
            user,
            'LOGIN',
            f'用户登录: {username}',
            user.id,
            'user'
        )
        
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.index')
            
        return redirect(next_page)
    
    # 生成验证码
    captcha_text, _ = generate_captcha()
    session['captcha_text'] = captcha_text.lower()
    session.modified = True
    
    return render_template('login.html')

@auth.route('/security-question/<int:user_id>', methods=['GET'])
def security_question(user_id):
    """显示安全问题验证页面"""
    # 检查是否通过了第一阶段身份验证
    pre_login_user_id = session.get('pre_login_user_id')
    if not pre_login_user_id or pre_login_user_id != user_id:
        flash('请先登录')
        return redirect(url_for('auth.login'))
    
    user = User.query.get_or_404(user_id)
    
    # 仅在特定用户(cv24051)设置了安全问题时显示验证页面
    if user.username != 'cv24051' or not user.security_question:
        flash('该用户不需要回答安全问题')
        return redirect(url_for('auth.login'))
    
    return render_template('security_question.html', user=user)

@auth.route('/verify-security-question/<int:user_id>', methods=['POST'])
def verify_security_question(user_id):
    """验证安全问题答案"""
    # 检查是否通过了第一阶段身份验证
    pre_login_user_id = session.get('pre_login_user_id')
    pre_login_username = session.get('pre_login_username')
    
    if not pre_login_user_id or pre_login_user_id != user_id:
        flash('请先登录')
        return redirect(url_for('auth.login'))
    
    user = User.query.get_or_404(user_id)
    
    # 获取并验证答案
    answer = request.form.get('answer', '')
    
    current_app.logger.info(f'安全问题验证: 用户={user.username}')
    
    if not user.check_security_answer(answer):
        flash('安全问题答案错误')
        current_app.logger.warning(f'安全问题验证失败: 用户={user.username}')
        return redirect(url_for('auth.security_question', user_id=user.id))
    
    # 验证成功，清除会话状态并登录用户
    session.pop('pre_login_user_id', None)
    session.pop('pre_login_username', None)
    session.pop('captcha_text', None)
    
    login_user(user)
    current_app.logger.info(f'用户 {user.username} 通过安全问题验证并登录成功')
    
    # 记录登录日志
    log_user_action(
        user,
        'LOGIN',
        f'用户通过安全问题验证登录: {user.username}',
        user.id,
        'user'
    )
    
    return redirect(url_for('main.index'))

@auth.route('/logout')
@login_required
def logout():
    username = current_user.username
    user_id = current_user.id
    
    # 记录退出日志
    log_user_action(
        current_user,
        'LOGOUT',
        f'用户退出: {username}',
        user_id,
        'user'
    )
    
    logout_user()
    current_app.logger.info(f'用户 {username} 退出登录')
    return redirect(url_for('auth.login'))

# 管理员路由
@main.route('/admin/users', methods=['GET', 'POST'])
@login_required
def admin_users():
    """用户管理页面"""
    if not can_access_feature(current_user, 'user_management'):
        flash(t('You do not have permission to access this feature'), 'warning')
        return redirect(url_for('main.index'))
    
    # 处理添加用户请求
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        
        # 超级管理员可以添加高级用户，普通高级用户只能添加普通用户
        if is_admin and current_user.username != 'cv24051':
            is_admin = False
            flash('只有超级管理员可以创建高级用户', 'warning')
        
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'danger')
        else:
            user = User(username=username, is_admin=is_admin)
            user.set_password(password)
            db.session.add(user)
            db.session.commit()
            
            # 记录操作日志
            log_user_action(
                current_user,
                'CREATE_USER',
                f'创建用户: {username}, 角色: {"高级用户" if is_admin else "普通用户"}'
            )
            
            flash('用户添加成功', 'success')
    
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@main.route('/admin/users/<int:user_id>/toggle_admin', methods=['POST'])
@login_required
def toggle_admin(user_id):
    # 将检查从is_admin改为检查用户名是否为'cv24051'
    if current_user.username != 'cv24051':
        flash('只有超级管理员(cv24051)可以修改用户权限')
        return redirect(url_for('main.admin_users'))
    
    try:
        user = User.query.get_or_404(user_id)
        
        # 不能修改cv24051用户的权限
        if user.username == 'cv24051':
            return jsonify({
                'success': False,
                'message': 'cv24051用户权限不能被修改'
            })
        
        # 不能修改自己的权限
        if user.id == current_user.id:
            return jsonify({
                'success': False,
                'message': '不能修改自己的高级用户权限'
            })
            
        user.is_admin = not user.is_admin
        db.session.commit()
        
        current_app.logger.info(
            f'超级管理员 {current_user.username} {"授予" if user.is_admin else "取消"} {user.username} 的高级用户权限'
        )
        
        return jsonify({
            'success': True,
            'message': '权限修改成功'
        })
        
    except Exception as e:
        current_app.logger.error(f'修改用户权限失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/admin/users/delete/<int:user_id>')
@login_required
def delete_user(user_id):
    # 将检查从is_admin改为检查用户名是否为'cv24051'
    if current_user.username != 'cv24051':
        flash('只有超级管理员(cv24051)可以删除用户')
        return redirect(url_for('main.admin_users'))
    
    try:
        user = User.query.get_or_404(user_id)
        
        # 不能删除cv24051用户
        if user.username == 'cv24051':
            flash('cv24051用户不能被删除')
            return redirect(url_for('main.admin_users'))
            
        # 不能删除当前登录用户
        if user.id == current_user.id:
            flash('不能删除当前登录用户')
            return redirect(url_for('main.admin_users'))
            
        # 删除用户的文件
        files = File.query.filter_by(user_id=user.id).all()
        for file in files:
            try:
                # 删除物理文件
                file_path = os.path.join(
                    current_app.config['UPLOAD_FOLDER'],
                    file.path,
                    file.filename
                )
                if os.path.exists(file_path):
                    os.remove(file_path)
                # 删除数据库记录
                db.session.delete(file)
            except Exception as e:
                current_app.logger.error(f'删除用户文件失败: {str(e)}')
                # 继续处理其他文件
                continue
            
        # 删除用户的文件夹
        folders = Folder.query.filter_by(user_id=user.id).all()
        for folder in folders:
            try:
                # 删除文件夹中的所有文件
                for file in folder.files:
                    try:
                        file_path = os.path.join(
                            current_app.config['UPLOAD_FOLDER'],
                            file.path,
                            file.filename
                        )
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        db.session.delete(file)
                    except Exception as e:
                        current_app.logger.error(f'删除文件夹中的文件失败: {str(e)}')
                        continue
                
                # 删除文件夹记录
                db.session.delete(folder)
            except Exception as e:
                current_app.logger.error(f'删除文件夹失败: {str(e)}')
                continue
            
        # 删除用户的日志
        try:
            UserLog.query.filter_by(user_id=user.id).delete()
        except Exception as e:
            current_app.logger.error(f'删除用户日志失败: {str(e)}')
            
        # 从其他用户的文件夹访问权限中移除此用户
        try:
            folders = Folder.query.filter(Folder.allowed_users.like(f'%{user.id}%')).all()
            for folder in folders:
                allowed_users = [str(id) for id in folder.allowed_users.split(',') if id and int(id) != user.id]
                folder.allowed_users = ','.join(allowed_users)
        except Exception as e:
            current_app.logger.error(f'更新文件夹权限失败: {str(e)}')
            
        # 最后删除用户
        db.session.delete(user)
        
        # 提交所有更改
        try:
            db.session.commit()
            flash('用户已删除')
            
            # 记录操作日志
            log_user_action(
                current_user,
                'DELETE_USER',
                f'删除用户: {user.username}'
            )
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'删除用户失败: {str(e)}')
            flash('删除用户失败')
            
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除用户失败: {str(e)}')
        flash('删除用户失败')
        
    return redirect(url_for('main.admin_users'))

# 主要功能路由
@main.route('/')
@main.route('/folder/<int:folder_id>')
@login_required
def index(folder_id=None):
    try:
        # 获取关键字筛选参数
        keyword1 = request.args.get('keyword1', '').strip()
        keyword2 = request.args.get('keyword2', '').strip()
        selected_folder_id = request.args.get('selected_folder_id', '').strip()
        
        # 获取搜索参数
        filename = request.args.get('filename', '').strip()
        filename_match = request.args.get('filenameMatch', 'fuzzy')
        tags = request.args.get('tags', '').strip()
        search_scope = request.args.get('searchScope', 'current')
        
        current_app.logger.info(f"关键字筛选请求 - 关键字1: {keyword1}, 关键字2: {keyword2}, 选择文件夹: {selected_folder_id}")
        current_app.logger.info(f"搜索请求 - 文件名: {filename}, 匹配方式: {filename_match}, 标签: {tags}, 范围: {search_scope}")
        
        # 获取当前文件夹
        current_folder = None
        if folder_id:
            current_folder = Folder.query.get_or_404(folder_id)
            if not current_folder.can_access(current_user):
                flash('您没有权限访问此文件夹')
                return redirect(url_for('main.index'))
        
        # 获取可访问的文件夹列表（用于文件夹下拉菜单）
        if current_user.is_admin:
            all_folders = Folder.query.filter_by(is_deleted=False).all()
        else:
            all_folders = Folder.query.filter(
                Folder.is_deleted == False,
                db.or_(
                    Folder.user_id == current_user.id,
                    Folder.is_public == True,
                    Folder.allowed_users.like(f'%{current_user.id}%')
                )
            ).all()
        
        # 构建面包屑导航
        breadcrumbs = []
        if current_folder:
            temp_folder = current_folder
            while temp_folder:
                breadcrumbs.insert(0, temp_folder)
                temp_folder = temp_folder.parent
        
        # 检查是否正在进行搜索或筛选
        is_searching_or_filtering = bool(keyword1 or keyword2 or filename or tags or selected_folder_id or search_scope != 'current')
        
        # 获取当前文件夹下的子文件夹（只有在不搜索/筛选时才显示）
        if current_folder and not is_searching_or_filtering:
            folders_query = Folder.query.filter_by(
                parent_id=current_folder.id,
                is_deleted=False
            )
            folders = folders_query.all()
            # 过滤文件夹列表，只显示用户有权限访问的文件夹
            folders = [f for f in folders if f.can_access(current_user)]
        elif not is_searching_or_filtering:
            folders_query = Folder.query.filter_by(
                parent_id=None,
                is_deleted=False
            )
            folders = folders_query.all()
            # 过滤文件夹列表，只显示用户有权限访问的文件夹
            folders = [f for f in folders if f.can_access(current_user)]
        else:
            # 当进行搜索或筛选时，不显示文件夹
            folders = []
        
        # 获取文件列表
        query = File.query.filter_by(is_deleted=False)
        
        # 处理文件夹筛选逻辑
        if selected_folder_id:
            try:
                selected_folder_id = int(selected_folder_id)
                selected_folder = Folder.query.get(selected_folder_id)
                
                if not selected_folder:
                    current_app.logger.error(f"找不到文件夹ID: {selected_folder_id}")
                    flash('选择的文件夹不存在')
                    return redirect(url_for('main.index', folder_id=folder_id))
                
                if not selected_folder.can_access(current_user):
                    current_app.logger.error(f"用户无权访问文件夹: {selected_folder_id}")
                    flash('您没有权限访问所选文件夹')
                    return redirect(url_for('main.index', folder_id=folder_id))
                
                # 获取选中文件夹及其所有子文件夹的ID
                folder_ids = get_all_subfolder_ids(selected_folder_id)
                query = query.filter(File.folder_id.in_(folder_ids))
            except ValueError:
                current_app.logger.error(f"无效的文件夹ID: {selected_folder_id}")
                flash('无效的文件夹选择')
                return redirect(url_for('main.index', folder_id=folder_id))
        elif filename or tags or search_scope != 'current':
            # 如果有搜索条件，但没有指定文件夹，根据搜索范围决定
            if search_scope == 'all':
                # 在所有文件夹中搜索，不添加文件夹筛选条件
                pass
            elif search_scope == 'sub' and current_folder:
                # 在当前文件夹及其子文件夹中搜索
                folder_ids = get_all_subfolder_ids(current_folder.id)
                query = query.filter(File.folder_id.in_(folder_ids))
            elif current_folder:
                # 只在当前文件夹中搜索
                query = query.filter(File.folder_id == current_folder.id)
            else:
                # 在主页上，且搜索范围为current时，只显示根文件夹的文件
                query = query.filter(File.folder_id == None)
        elif current_folder:
            # 如果当前在某个文件夹中，且没有搜索条件，显示该文件夹的文件
            query = query.filter(File.folder_id == current_folder.id)
        else:
            # 在主页上，且没有搜索条件，显示根文件夹的文件
            query = query.filter(File.folder_id == None)
        
        # 应用关键字筛选
        if keyword1 or keyword2:
            conditions = []
            
            if keyword1:
                conditions.append(
                    db.or_(
                        File.original_filename.ilike(f'%{keyword1}%'),
                        File.tags.ilike(f'%{keyword1}%')
                    )
                )
            
            if keyword2:
                conditions.append(
                    db.or_(
                        File.original_filename.ilike(f'%{keyword2}%'),
                        File.tags.ilike(f'%{keyword2}%')
                    )
                )
            
            if conditions:
                query = query.filter(db.and_(*conditions))
        
        # 应用文件名和标签搜索
        if filename or tags:
            search_conditions = []
            
            if filename:
                if filename_match == 'exact':
                    search_conditions.append(File.original_filename == filename)
                else:
                    search_conditions.append(File.original_filename.ilike(f'%{filename}%'))
            
            if tags:
                # 分割多个标签，需要文件同时包含所有指定的标签
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                for tag in tag_list:
                    search_conditions.append(File.tags.ilike(f'%{tag}%'))
            
            if search_conditions:
                query = query.filter(db.and_(*search_conditions))
        
        # 获取用户有权限访问的文件夹ID列表（包括用户组权限）
        accessible_folder_ids = []
        if not current_user.is_admin:
            # 用户自己的文件夹
            user_folders = Folder.query.filter_by(user_id=current_user.id, is_deleted=False).all()
            for folder in user_folders:
                accessible_folder_ids.append(folder.id)
            
            # 公开的文件夹
            public_folders = Folder.query.filter_by(is_public=True, is_deleted=False).all()
            for folder in public_folders:
                if folder.id not in accessible_folder_ids:
                    accessible_folder_ids.append(folder.id)
            
            # 用户被直接授权的文件夹
            shared_folders = Folder.query.filter(
                Folder.is_deleted == False,
                Folder.allowed_users.like(f'%{current_user.id}%')
            ).all()
            for folder in shared_folders:
                if folder.id not in accessible_folder_ids:
                    accessible_folder_ids.append(folder.id)
            
            # 通过用户组共享的文件夹
            user_groups = current_user.groups.all()
            user_group_ids = [group.id for group in user_groups]
            
            group_shared_folders = Folder.query.filter(
                Folder.is_deleted == False
            ).all()
            
            for folder in group_shared_folders:
                if folder.id in accessible_folder_ids:
                    continue
                    
                if folder.allowed_groups:
                    allowed_group_ids = [int(id.strip()) for id in folder.allowed_groups.split(',') if id.strip()]
                    if any(group_id in allowed_group_ids for group_id in user_group_ids):
                        accessible_folder_ids.append(folder.id)
            
            # 过滤文件列表，只显示用户有权访问的文件
            # 1. 用户自己的文件
            # 2. 在用户有权访问的文件夹中的文件
            query = query.filter(db.or_(
                File.user_id == current_user.id,
                File.folder_id.in_(accessible_folder_ids) if accessible_folder_ids else False,
                File.folder_id == None
            ))
        
        # 获取文件列表
        files = query.order_by(File.original_filename).all()
        
        # 检查是否处于筛选模式
        is_filtering = bool(keyword1 or keyword2 or filename or tags or search_scope != 'current')
        
        return render_template('index.html',
                             current_folder=current_folder,
                             folders=folders,
                             files=files,
                             all_folders=all_folders,
                             breadcrumbs=breadcrumbs,
                             keyword1=keyword1,
                             keyword2=keyword2,
                             selected_folder_id=selected_folder_id,
                             filename=filename,
                             filename_match=filename_match,
                             tags=tags,
                             search_scope=search_scope,
                             is_filtering=is_filtering,
                             users=User.query.all() if current_user.is_admin else [])
                             
    except Exception as e:
        current_app.logger.error(f"index页面加载错误: {str(e)}")
        flash('加载页面时出现错误')
        return redirect(url_for('main.index'))

def save_file(file, folder_id=None):
    try:
        # 获取安全的文件名（如果是文件夹上传，只使用最后部分的文件名）
        if '/' in file.filename:
            # 从文件路径中提取文件名
            filename = secure_filename(file.filename.split('/')[-1])
        else:
            filename = secure_filename(file.filename)
        
        # 尝试猜测文件类型
        file_type = file.content_type or 'application/octet-stream'
        
        # 获取文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)  # 重置文件指针
        
        # 确定存储路径
        folder = None
        if folder_id:
            folder = Folder.query.get(folder_id)
            if folder:
                relative_path = folder.path or os.path.join('user_' + str(current_user.id), 'folder_' + str(folder.id))
                storage_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
            else:
                raise ValueError(f'找不到文件夹: {folder_id}')
        else:
            # 如果没有指定文件夹，使用用户专属目录
            relative_path = os.path.join('user_' + str(current_user.id))
            storage_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
        
        # 确保存储目录存在
        if not os.path.exists(storage_path):
            os.makedirs(storage_path)
            
        # 为避免文件名冲突，使用时间戳
        base, ext = os.path.splitext(filename)
        unique_filename = f"{base}_{int(time.time())}{ext}"
        file_path = os.path.join(storage_path, unique_filename)
        
        # 保存文件
        file.save(file_path)
        
        # 创建数据库记录
        new_file = File(
            filename=unique_filename,
            original_filename=filename,  # 只存储文件名，不包含路径
            file_type=file_type,
            file_size=file_size,
            path=relative_path,
            folder_id=folder_id,
            user_id=current_user.id
        )
        
        db.session.add(new_file)
        db.session.flush()  # 获取文件ID，但不提交事务
        
        current_app.logger.info(
            f'用户 {current_user.username} 上传文件: {new_file.original_filename} 到文件夹: {folder.name if folder else "根目录"}'
        )
        
        # 记录日志
        log_user_action(
            current_user, 
            'UPLOAD', 
            f'上传文件: {filename}'
        )
        
        return new_file
        
    except Exception as e:
        current_app.logger.error(f'保存文件失败: {str(e)}')
        # 如果保存失败，清理已创建的文件
        try:
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
        except:
            pass
        return None
        
@main.route('/upload', methods=['POST'])
@login_required
def upload_file():
    try:
        upload_type = request.form.get('file_type', 'file')
        folder_id = request.form.get('folder_id', type=int)
        tags = request.form.get('tags', '').strip()  # 获取标签
        
        # 如果指定了文件夹，检查权限
        if folder_id:
            folder = Folder.query.get_or_404(folder_id)
            # 检查是否有权限访问此文件夹
            if not current_user.is_admin and not folder.can_access(current_user):
                return jsonify({
                    'success': False,
                    'message': '没有权限上传到此文件夹'
                })
            # 检查文件夹是否为只读
            if folder.read_only and not current_user.is_admin:
                return jsonify({
                    'success': False,
                    'message': '此文件夹为只读模式'
                })
        
        if upload_type == 'file':
            files = request.files.getlist('file')
            if not files or not files[0].filename:
                raise ValueError('没有选择文件')
            
            uploaded_files = []
            for file in files:
                if file.filename:
                    # 检查同名文件
                    if folder_id:
                        existing_file = File.query.filter_by(
                            folder_id=folder_id,
                            original_filename=file.filename,
                            is_deleted=False
                        ).first()
                    else:
                        existing_file = File.query.filter_by(
                            folder_id=None,
                            user_id=current_user.id,
                            original_filename=file.filename,
                            is_deleted=False
                        ).first()
                        
                    if existing_file:
                        return jsonify({
                            'success': False,
                            'message': f'文件 {file.filename} 已存在'
                        })
                    
                    new_file = save_file(file, folder_id)
                    if new_file:
                        # 保存标签
                        if tags:
                            new_file.tags = tags
                        db.session.add(new_file)
                        uploaded_files.append(new_file.original_filename)
            
            # 记录上传操作
            folder_name = folder.name if folder_id and folder else "根目录"
            log_user_action(
                current_user,
                'UPLOAD',
                f'上传文件到 {folder_name}: {", ".join(uploaded_files)}'
            )
            
        elif upload_type == 'folder':
            if 'folder' not in request.files:
                raise ValueError('没有选择文件夹')
                
            files = request.files.getlist('folder')
            if not files or not files[0].filename:
                raise ValueError('文件夹为空')
            
            # 解析文件夹结构
            folders_structure = {}  # 存储路径到文件夹ID的映射
            
            # 首先获取文件夹名称（从第一个文件的路径中提取）
            first_file_path = files[0].filename
            parts = first_file_path.split('/')
            root_folder_name = parts[0]
            
            # 检查根文件夹是否已存在
            existing_root_folder = Folder.query.filter_by(
                name=root_folder_name,
                parent_id=folder_id,
                user_id=current_user.id,
                is_deleted=False
            ).first()
            
            if existing_root_folder:
                root_folder = existing_root_folder
                current_app.logger.info(f'使用已存在的根文件夹: {root_folder_name}')
            else:
                # 创建根文件夹
                root_folder = Folder(
                    name=root_folder_name,
                    parent_id=folder_id,
                    user_id=current_user.id,
                    is_public=False,
                    read_only=False,
                    allowed_users=''
                )
                
                # 如果有父文件夹，继承其权限设置
                if folder_id:
                    parent_folder = Folder.query.get(folder_id)
                    if parent_folder:
                        root_folder.is_public = parent_folder.is_public
                        root_folder.read_only = parent_folder.read_only
                        root_folder.allowed_users = parent_folder.allowed_users
                
                # 保存标签到文件夹
                if tags:
                    root_folder.tags = tags
                    
                db.session.add(root_folder)
                db.session.flush()  # 获取文件夹ID
            
            folders_structure[''] = root_folder.id  # 空字符串表示根路径
            
            # 处理所有文件，确保目录结构先创建
            for file in files:
                if not file.filename:
                    continue
                    
                path_parts = file.filename.split('/')
                filename = path_parts[-1]
                
                # 跳过目录自身（没有文件名的路径）
                if not filename:
                    continue
                
                # 确定文件所在的文件夹路径
                folder_path = '/'.join(path_parts[1:-1])  # 去除根文件夹名和文件名
                
                # 确保文件夹路径存在
                if folder_path and folder_path not in folders_structure:
                    current_path = ''
                    current_parent_id = root_folder.id
                    
                    # 逐级创建子文件夹
                    for folder_name in folder_path.split('/'):
                        if not folder_name:
                            continue
                            
                        new_path = current_path + ('/' if current_path else '') + folder_name
                        
                        if new_path in folders_structure:
                            current_parent_id = folders_structure[new_path]
                        else:
                            # 检查此级文件夹是否已存在
                            existing_folder = Folder.query.filter_by(
                                name=folder_name,
                                parent_id=current_parent_id,
                                user_id=current_user.id,
                                is_deleted=False
                            ).first()
                            
                            if existing_folder:
                                current_parent_id = existing_folder.id
                            else:
                                # 创建新的子文件夹
                                sub_folder = Folder(
                                    name=folder_name,
                                    parent_id=current_parent_id,
                                    user_id=current_user.id,
                                    is_public=root_folder.is_public,
                                    read_only=root_folder.read_only,
                                    allowed_users=root_folder.allowed_users,
                                    tags=tags if tags else None
                                )
                                db.session.add(sub_folder)
                                db.session.flush()
                                current_parent_id = sub_folder.id
                                
                                # 记录创建子文件夹操作
                                log_user_action(
                                    current_user,
                                    'CREATE_FOLDER',
                                    f'在上传文件夹过程中创建子文件夹: {folder_name}'
                                )
                                
                            folders_structure[new_path] = current_parent_id
                            
                        current_path = new_path
                
                # 确定文件应该保存到哪个文件夹
                target_folder_id = folders_structure.get(folder_path, root_folder.id)
                
                # 检查文件是否已存在
                existing_file = File.query.filter_by(
                    folder_id=target_folder_id,
                    original_filename=filename,
                    is_deleted=False
                ).first()
                
                if existing_file:
                    current_app.logger.warning(f'文件已存在，跳过: {file.filename}')
                    continue
                
                # 保存文件
                new_file = save_file(file, target_folder_id)
                if new_file:
                    if tags:
                        new_file.tags = tags
                    db.session.add(new_file)
            
            # 记录上传操作
            parent_folder_name = "根目录"
            if folder_id:
                parent_folder = Folder.query.get(folder_id)
                if parent_folder:
                    parent_folder_name = parent_folder.name
                    
            log_user_action(
                current_user,
                'UPLOAD_FOLDER',
                f'上传文件夹 {root_folder_name} 到 {parent_folder_name}'
            )
            
        db.session.commit()
        return jsonify({
            'success': True,
            'message': '上传成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'上传失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not current_user.check_password(old_password):
            flash('原密码错误')
            return redirect(url_for('main.change_password'))
            
        if new_password != confirm_password:
            flash('两次输入的新密码不一致')
            return redirect(url_for('main.change_password'))
            
        current_user.set_password(new_password)
        db.session.commit()
        flash('密码修改成功')
        return redirect(url_for('main.index'))
        
    return render_template('change_password.html')

@main.route('/delete_files', methods=['POST'])
@login_required
def delete_files():
    try:
        data = request.get_json()
        file_ids = data.get('file_ids', [])
        
        files = File.query.filter(
            File.id.in_(file_ids),
            File.user_id == current_user.id
        ).all()
        
        for file in files:
            current_app.logger.info(
                f'用户 {current_user.username} 删除文件: {file.original_filename}'
            )
            # 移动到回收站而不是直接删除
            file.is_deleted = True
            file.delete_time = datetime.now()
            
            # 记录操作日志
            log_user_action(
                current_user,
                'MOVE_TO_TRASH',
                f'将文件移动到回收站: {file.original_filename}'
            )
        
        db.session.commit()
        return jsonify({'success': True, 'message': f'成功将 {len(files)} 个文件移动到回收站'})
        
    except Exception as e:
        current_app.logger.error(f'用户 {current_user.username} 删除文件失败: {str(e)}')
        return jsonify({'success': False, 'message': str(e)})

@main.route('/download/<int:file_id>')
@login_required
def download_file(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限下载此文件')
                return redirect(url_for('main.index'))
        
        # 获取文件路径
        file_path = os.path.join(
            current_app.config['UPLOAD_FOLDER'],
            file.path,
            file.filename
        )
        
        if not os.path.exists(file_path):
            flash('文件不存在')
            return redirect(url_for('main.index'))
            
        # 记录下载操作
        log_user_action(
            current_user, 
            'DOWNLOAD', 
            f'下载文件: {file.original_filename}',
            file.id,
            'file'
        )
        
        # 处理范围请求，支持断点续传
        file_size = os.path.getsize(file_path)
        range_header = request.headers.get('Range', None)
        
        if range_header:
            # 解析Range头，例如: "bytes=0-1023"
            byte_start, byte_end = 0, None
            byte_range = range_header.replace('bytes=', '').split('-')
            if byte_range[0]:
                byte_start = int(byte_range[0])
            if byte_range[1]:
                byte_end = int(byte_range[1])
                
            # 如果没有指定结束位置，使用文件大小作为结束位置
            if byte_end is None:
                byte_end = file_size - 1
            
            # 计算响应内容长度
            length = byte_end - byte_start + 1
            
            # 创建部分响应
            resp = Response(
                self=current_app.response_class(),
                status=206,
                mimetype=file.file_type
            )
            resp.headers.add('Content-Range', f'bytes {byte_start}-{byte_end}/{file_size}')
            resp.headers.add('Accept-Ranges', 'bytes')
            resp.headers.add('Content-Length', str(length))
            resp.headers.add('Content-Disposition', f'attachment; filename="{file.original_filename}"')
            
            # 打开文件并设置游标到请求的字节范围
            f = open(file_path, 'rb')
            f.seek(byte_start)
            resp.data = f.read(length)
            f.close()
            
            return resp
        else:
            # 添加缓存控制头
            response = send_file(
                file_path,
                as_attachment=True,
                download_name=file.original_filename,
                mimetype=file.file_type
            )
            
            # 文件上传时间作为Last-Modified
            if file.upload_date:
                response.headers['Last-Modified'] = file.upload_date.strftime('%a, %d %b %Y %H:%M:%S GMT')
            
            # 添加缓存控制头
            response.headers['Cache-Control'] = 'max-age=60'  # 允许客户端缓存60秒
            response.headers['Accept-Ranges'] = 'bytes'  # 表明服务器支持范围请求
            
            return response
        
    except Exception as e:
        current_app.logger.error(f'下载文件失败: {str(e)}')
        flash('下载文件失败')
        return redirect(url_for('main.index'))

@main.route('/delete_file/<int:file_id>', methods=['POST'])
@login_required
def delete_file(file_id):
    try:
        file = File.query.get_or_404(file_id)
        current_app.logger.info(f'开始删除文件: {file.original_filename}')
        
        # 检查权限
        if not is_super_admin(current_user) and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限删除此文件'
            })
            
        # 移动到回收站
        file.is_deleted = True
        file.delete_time = datetime.now()
        current_app.logger.info(f'文件标记为已删除: {file.is_deleted}, 删除时间: {file.delete_time}')
        
        db.session.commit()
        current_app.logger.info('数据库更新成功')
        
        # 记录操作日志
        log_user_action(
            current_user,
            'MOVE_TO_TRASH',
            f'将文件移动到回收站: {file.original_filename}'
        )
        
        return jsonify({
            'success': True,
            'message': '文件已移动到回收站'
        })
        
    except Exception as e:
        current_app.logger.error(f'删除文件失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除失败'
        })

@main.route('/file/preview/<int:file_id>')
@login_required
def preview_file(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限预览此文件')
                return redirect(url_for('main.index'))
        
        # 获取文件路径
        file_path = os.path.join(
            current_app.config['UPLOAD_FOLDER'],
            file.path,
            file.filename
        )
        
        if not os.path.exists(file_path):
            flash('文件不存在')
            return redirect(url_for('main.index'))
            
        # 记录预览操作
        log_user_action(
            current_user, 
            'PREVIEW', 
            f'预览文件: {file.original_filename}',
            file.id,
            'file'
        )
        
        # 根据文件类型返回不同的预览方式
        mime_type = file.file_type or 'application/octet-stream'
        
        # 图片直接显示
        if mime_type.startswith('image/'):
            return send_file(file_path, mimetype=mime_type)
            
        # PDF直接显示并添加签名选项
        elif mime_type == 'application/pdf':
            # 构建相对路径用于模板
            static_folder = current_app.static_folder
            upload_folder = current_app.config['UPLOAD_FOLDER']
            
            # 检查文件是否在static目录下
            if file_path.startswith(static_folder):
                relative_path = file_path[len(static_folder):].lstrip(os.path.sep)
                file_url = url_for('static', filename=relative_path)
            else:
                # 如果不在static目录下，使用流式传输方式
                file_url = url_for('main.stream_file', file_id=file.id)
            
            return render_template('preview/pdf.html',
                filename=file.original_filename,
                file_url=file_url,
                file_id=file.id
            )
            
        # 文本文件直接显示
        elif mime_type.startswith('text/') or mime_type in [
            'application/json',
            'application/xml',
            'application/javascript',
            'application/x-python',
            'application/x-yaml'
        ]:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return render_template('preview/text.html', 
                    content=content,
                    filename=file.original_filename,
                    file_type=mime_type
                )
            except UnicodeDecodeError:
                return render_template('preview/binary.html',
                    filename=file.original_filename,
                    file_type=mime_type,
                    file_id=file.id
                )
            
        # 视频文件使用video标签播放
        elif mime_type.startswith('video/'):
            return render_template('preview/video.html',
                filename=file.original_filename,
                file_path=f'/stream/{file.id}',
                file_type=mime_type
            )
            
        # 音频文件使用audio标签播放
        elif mime_type.startswith('audio/'):
            return render_template('preview/audio.html',
                filename=file.original_filename,
                file_path=f'/stream/{file.id}',
                file_type=mime_type
            )
            
        # 其他类型文件显示下载选项
        else:
            return render_template('preview/binary.html',
                filename=file.original_filename,
                file_type=mime_type,
                file_id=file.id
            )
            
    except Exception as e:
        current_app.logger.error(f'预览文件失败: {str(e)}')
        flash('预览文件失败')
        return redirect(url_for('main.index'))

# 添加流媒体路由用于视频和音频播放
@main.route('/stream/<int:file_id>')
@login_required
def stream_file(file_id):
    file = File.query.get_or_404(file_id)
    
    # 检查权限
    if not current_user.is_admin:
        if not file.folder or not file.folder.can_access(current_user):
            abort(403)
    
    file_path = os.path.join(
        current_app.config['UPLOAD_FOLDER'],
        file.path,
        file.filename
    )
    
    if not os.path.exists(file_path):
        abort(404)
        
    return send_file(
        file_path,
        mimetype=file.file_type,
        as_attachment=False
    )

@main.route('/create_folder', methods=['POST'])
@login_required
def create_folder():
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        parent_id = data.get('parent_id')
        is_public = data.get('is_public', False)
        read_only = data.get('read_only', False)
        allowed_users = data.get('allowed_users', [])
        
        if not name:
            return jsonify({
                'success': False,
                'message': '文件夹名称不能为空'
            })
            
        # 检查父文件夹权限
        if parent_id:
            parent_folder = Folder.query.get_or_404(parent_id)
            if not current_user.is_admin and not parent_folder.can_access(current_user):
                return jsonify({
                    'success': False,
                    'message': '没有权限在此创建文件夹'
                })
            if parent_folder.read_only and not current_user.is_admin:
                return jsonify({
                    'success': False,
                    'message': '此文件夹为只读模式'
                })
        
        # 检查同名文件夹
        existing_folder = Folder.query.filter_by(
            name=name,
            parent_id=parent_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if existing_folder:
            return jsonify({
                'success': False,
                'message': '同名文件夹已存在'
            })
            
        # 创建新文件夹
        new_folder = Folder(
            name=name,
            parent_id=parent_id,
            user_id=current_user.id,
            is_public=is_public,
            read_only=read_only,
            allowed_users=','.join(map(str, allowed_users))
        )
        
        db.session.add(new_folder)
        db.session.commit()
        
        # 记录操作日志
        parent_name = "根目录"
        if parent_id:
            parent_folder = Folder.query.get(parent_id)
            if parent_folder:
                parent_name = parent_folder.name
                
        log_user_action(
            current_user,
            'CREATE_FOLDER',
            f'在 {parent_name} 中创建文件夹: {name}'
        )
        
        # 返回包含新创建的文件夹ID
        return jsonify({
            'success': True,
            'message': '文件夹创建成功',
            'folder_id': new_folder.id
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建文件夹失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/delete_folder/<int:folder_id>', methods=['POST'])
@login_required
def delete_folder(folder_id):
    try:
        folder = Folder.query.get_or_404(folder_id)
        current_app.logger.info(f'开始删除文件夹: {folder.name}')
        
        # 检查权限
        if not current_user.is_admin and folder.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限删除此文件夹'
            })
            
        # 递归将文件夹及其内容移动到回收站
        def move_to_trash(current_folder):
            current_app.logger.info(f'处理文件夹: {current_folder.name}')
            
            # 保存原始路径信息用于恢复
            if not current_folder.path:
                current_folder.path = str(current_folder.parent_id) if current_folder.parent_id else ''
            
            # 移动当前文件夹到回收站
            current_folder.is_deleted = True
            current_folder.delete_time = datetime.now()
            current_app.logger.info(f'文件夹标记为已删除: {current_folder.is_deleted}')
            
            # 移动文件夹中的文件到回收站
            for file in current_folder.files:
                file.is_deleted = True
                file.delete_time = datetime.now()
                current_app.logger.info(f'文件标记为已删除: {file.original_filename}')
            
            # 递归处理子文件夹
            for subfolder in current_folder.subfolders:
                move_to_trash(subfolder)
        
        # 执行移动到回收站
        move_to_trash(folder)
        db.session.commit()
        current_app.logger.info('数据库更新成功')
        
        # 记录操作日志
        log_user_action(
            current_user,
            'MOVE_TO_TRASH',
            f'将文件夹移动到回收站: {folder.name}'
        )
        
        return jsonify({
            'success': True,
            'message': '文件夹已移动到回收站'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除文件夹失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除失败'
        })

@main.route('/update_tags', methods=['POST'])
@login_required
def update_tags():
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        tags = data.get('tags', '')
        
        file = File.query.filter_by(id=file_id, user_id=current_user.id).first_or_404()
        file.tags = tags
        db.session.commit()
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@main.route('/batch_update_tags', methods=['POST'])
@login_required
def batch_update_tags():
    try:
        data = request.get_json()
        file_ids = data.get('file_ids', [])
        folder_ids = data.get('folder_ids', [])
        action = data.get('action')
        tag = data.get('tag', '').strip()
        
        if not tag:
            raise ValueError('标签不能为空')
            
        # 更新文件标签
        files = File.query.filter(
            File.id.in_(file_ids),
            File.user_id == current_user.id
        ).all()
        
        for file in files:
            current_tags = set(file.tags.split(',')) if file.tags else set()
            if action == 'add':
                current_tags.add(tag)
            elif action == 'remove':
                current_tags.discard(tag)
            file.tags = ','.join(filter(None, current_tags))
        
        # 更新文件夹标签
        folders = Folder.query.filter(
            Folder.id.in_(folder_ids),
            Folder.user_id == current_user.id
        ).all()
        
        for folder in folders:
            current_tags = set(folder.tags.split(',')) if folder.tags else set()
            if action == 'add':
                current_tags.add(tag)
            elif action == 'remove':
                current_tags.discard(tag)
            folder.tags = ','.join(filter(None, current_tags))
        
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@main.route('/file/update_tag', methods=['POST'])
@login_required
def update_tag():
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        old_tag = data.get('old_tag')
        new_tag = data.get('new_tag')
        
        file = File.query.get_or_404(file_id)
        
        # 检查权限：文件所有者或管理员可以修改标签
        if not current_user.is_admin and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限修改此文件的标签'
            })
            
        # 获取当前标签列表
        current_tags = [tag.strip() for tag in (file.tags.split(',') if file.tags else []) if tag.strip()]
        
        # 更新标签
        if old_tag in current_tags:
            current_tags.remove(old_tag)
        if new_tag and new_tag not in current_tags:
            current_tags.append(new_tag)
            
        # 保存更新后的标签
        file.tags = ','.join(current_tags)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'UPDATE_TAG',
            f'修改文件标签: {file.original_filename}, {old_tag} -> {new_tag}'
        )
        
        return jsonify({
            'success': True,
            'message': '标签更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新标签失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新标签失败'
        })

@main.route('/file/remove_tag', methods=['POST'])
@login_required
def remove_tag():
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        tag = data.get('tag')
        
        file = File.query.get_or_404(file_id)
        
        # 检查权限：文件所有者或管理员可以删除标签
        if not current_user.is_admin and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限修改此文件的标签'
            })
            
        # 获取当前标签列表
        current_tags = [t.strip() for t in file.tags.split(',') if t.strip()]
        
        # 移除标签
        if tag in current_tags:
            current_tags.remove(tag)
            
        # 保存更新后的标签
        file.tags = ','.join(current_tags)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'REMOVE_TAG',
            f'删除文件标签: {file.original_filename}, {tag}'
        )
        
        return jsonify({
            'success': True,
            'message': '标签删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除标签失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除标签失败'
        })

@main.route('/file/add_tag', methods=['POST'])
@login_required
def add_tag():
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        new_tag = data.get('tag')
        
        if not new_tag:
            return jsonify({
                'success': False,
                'message': '标签不能为空'
            })
            
        file = File.query.get_or_404(file_id)
        
        # 检查权限：文件所有者或管理员可以添加标签
        if not current_user.is_admin and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限修改此文件的标签'
            })
            
        # 获取当前标签列表
        current_tags = [tag.strip() for tag in (file.tags.split(',') if file.tags else []) if tag.strip()]
        
        # 添加新标签
        if new_tag not in current_tags:
            current_tags.append(new_tag)
            
        # 保存更新后的标签
        file.tags = ','.join(current_tags)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'ADD_TAG',
            f'添加文件标签: {file.original_filename}, {new_tag}'
        )
        
        return jsonify({
            'success': True,
            'message': '标签添加成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'添加标签失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '添加标签失败'
        })

@main.route('/update_folder_tag', methods=['POST'])
@login_required
def update_folder_tag():
    try:
        data = request.get_json()
        folder_id = data.get('folder_id')
        old_tag = data.get('old_tag')
        new_tag = data.get('new_tag', '').strip()
        
        if not new_tag:
            raise ValueError('标签不能为空')
            
        folder = Folder.query.filter_by(id=folder_id, user_id=current_user.id).first_or_404()
        tags = folder.tags.split(',') if folder.tags else []
        if old_tag in tags:
            tags[tags.index(old_tag)] = new_tag
            folder.tags = ','.join(filter(None, tags))
            db.session.commit()
            
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@main.route('/add_folder_tag', methods=['POST'])
@login_required
def add_folder_tag():
    try:
        data = request.get_json()
        folder_id = data.get('folder_id')
        tag = data.get('tag', '').strip()
        
        if not tag:
            raise ValueError('标签不能为空')
            
        folder = Folder.query.filter_by(id=folder_id, user_id=current_user.id).first_or_404()
        current_tags = set(folder.tags.split(',')) if folder.tags else set()
        current_tags.add(tag)
        folder.tags = ','.join(filter(None, current_tags))
        db.session.commit()
            
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@main.route('/remove_folder_tag', methods=['POST'])
@login_required
def remove_folder_tag():
    try:
        data = request.get_json()
        folder_id = data.get('folder_id')
        tag = data.get('tag', '').strip()
        
        if not tag:
            raise ValueError('标签不能为空')
            
        folder = Folder.query.filter_by(id=folder_id, user_id=current_user.id).first_or_404()
        current_tags = set(folder.tags.split(',')) if folder.tags else set()
        current_tags.discard(tag)
        folder.tags = ','.join(filter(None, current_tags))
        db.session.commit()
            
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@main.route('/search')
@login_required
def search():
    try:
        # 获取搜索参数
        keyword1 = request.args.get('keyword1', '').strip()
        keyword2 = request.args.get('keyword2', '').strip()
        selected_folder_id = request.args.get('selected_folder_id', '').strip()
        
        # 获取搜索参数
        filename = request.args.get('filename', '').strip()
        filename_match = request.args.get('filenameMatch', 'fuzzy')
        tags = request.args.get('tags', '').strip()
        search_scope = request.args.get('searchScope', 'current')
        current_folder_id = request.args.get('current_folder_id', type=int)
        
        # 如果没有任何搜索条件，返回空结果
        if not any([filename, keyword1, keyword2, tags]):
            return jsonify({
                'success': True,
                'results': []
            })
        
        # 构建基础查询
        folder_query = Folder.query
        file_query = File.query
        
        # 权限过滤
        if not current_user.is_admin:
            folder_query = folder_query.filter(
                db.or_(
                    Folder.user_id == current_user.id,
                    Folder.is_public == True,
                    Folder.allowed_users.like(f'%{current_user.id}%')
                )
            )
            accessible_folders = [f.id for f in folder_query.all()]
            file_query = file_query.filter(
                db.or_(
                    File.user_id == current_user.id,
                    File.folder_id.in_(accessible_folders)
                )
            )
        
        # 处理文件名搜索
        if filename:
            # 使用 ilike 进行不区分大小写的模糊匹配
            folder_query = folder_query.filter(Folder.name.ilike(f'%{filename}%'))
            file_query = file_query.filter(File.original_filename.ilike(f'%{filename}%'))
        
        # 处理文件类型搜索
        if keyword1 or keyword2:
            file_query = file_query.filter(
                db.or_(
                    File.original_filename.ilike(f'%{keyword1}%'),
                    File.tags.ilike(f'%{keyword1}%'),
                    File.original_filename.ilike(f'%{keyword2}%'),
                    File.tags.ilike(f'%{keyword2}%')
                )
            )
            # 文件夹不需要类型过滤
            folder_query = folder_query.filter(Folder.id == None)
        
        # 处理标签搜索
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            if tag_list:
                folder_conditions = []
                file_conditions = []
                for tag in tag_list:
                    folder_conditions.append(Folder.tags.ilike(f'%{tag}%'))
                    file_conditions.append(File.tags.ilike(f'%{tag}%'))
                folder_query = folder_query.filter(db.and_(*folder_conditions))
                file_query = file_query.filter(db.and_(*file_conditions))
        
        # 处理搜索范围
        if search_scope == 'current' and current_folder_id:
            folder_query = folder_query.filter_by(parent_id=current_folder_id)
            file_query = file_query.filter_by(folder_id=current_folder_id)
        
        # 执行查询
        folders = folder_query.all()
        files = file_query.all()
        
        # 记录搜索日志
        current_app.logger.info(
            f'用户 {current_user.username} 搜索 - ' +
            f'文件名:"{filename}", 类型:"{keyword1} {keyword2}", 标签:"{tags}" - ' +
            f'找到 {len(folders)} 个文件夹和 {len(files)} 个文件'
        )
        
        # 返回结果
        results = []
        for folder in folders:
            results.append({
                'type': 'folder',
                'id': folder.id,
                'name': folder.name,
                'create_date': folder.create_date.strftime('%Y-%m-%d %H:%M:%S'),
                'tags': folder.tags or '',
                'is_public': folder.is_public,
                'read_only': folder.read_only,
                'owner_name': folder.owner.username
            })
        
        for file in files:
            results.append({
                'type': 'file',
                'id': file.id,
                'original_filename': file.original_filename,
                'file_type': file.file_type,
                'file_size': file.file_size,
                'upload_date': file.upload_date.strftime('%Y-%m-%d %H:%M:%S'),
                'tags': file.tags or '',
                'owner_name': file.owner.username
            })
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        current_app.logger.error(f'搜索失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/move_item', methods=['POST'])
@login_required
def move_item():
    try:
        data = request.get_json()
        item_id = data.get('item_id')
        item_type = data.get('item_type')
        target_folder_id = data.get('target_folder_id')
        
        if not all([item_id, item_type]):
            return jsonify({
                'success': False,
                'message': '参数错误'
            })
        
        # 如果target_folder_id是空字符串，设置为None表示移动到根目录
        target_folder_id = target_folder_id or None
        
        # 检查目标文件夹（如果不是根目录）
        if target_folder_id:
            target_folder = Folder.query.get(target_folder_id)
            if not target_folder or target_folder.user_id != current_user.id:
                return jsonify({
                    'success': False,
                    'message': '目标文件夹不存在或无权限'
                })
            
        if item_type == 'file':
            item = File.query.get(item_id)
            if not item or item.user_id != current_user.id:
                return jsonify({
                    'success': False,
                    'message': '文件不存在或无权限'
                })
            item.folder_id = target_folder_id
            
        elif item_type == 'folder':
            item = Folder.query.get(item_id)
            if not item or item.user_id != current_user.id:
                return jsonify({
                    'success': False,
                    'message': '文件夹不存在或无权限'
                })
                
            if target_folder_id:
                # 检查是否移动到自己或子文件夹
                current = target_folder
                while current:
                    if current.id == item.id:
                        return jsonify({
                            'success': False,
                            'message': '不能移动到自己或子文件夹中'
                        })
                    current = current.parent
                
            item.parent_id = target_folder_id
            
        db.session.commit()
        return jsonify({'success': True})
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/rename_file', methods=['POST'])
@login_required
def rename_file():
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        new_name = data.get('new_name', '').strip()
        
        if not new_name:
            return jsonify({
                'success': False,
                'message': '文件名不能为空'
            })
        
        file = File.query.filter_by(id=file_id, user_id=current_user.id).first_or_404()
        
        # 检查同名文件
        same_folder_files = File.query.filter_by(
            folder_id=file.folder_id,
            user_id=current_user.id
        ).all()
        
        if any(f.original_filename == new_name and f.id != file_id for f in same_folder_files):
            return jsonify({
                'success': False,
                'message': '同一文件夹下已存在同名文件'
            })
        
        # 更新文件名
        file.original_filename = new_name
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件名修改成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/rename_folder', methods=['POST'])
@login_required
def rename_folder():
    try:
        data = request.get_json()
        folder_id = data.get('folder_id')
        new_name = data.get('new_name', '').strip()
        
        if not new_name:
            return jsonify({
                'success': False,
                'message': '文件夹名不能为空'
            })
        
        folder = Folder.query.filter_by(id=folder_id, user_id=current_user.id).first_or_404()
        
        # 检查同名文件夹
        same_parent_folders = Folder.query.filter_by(
            parent_id=folder.parent_id,
            user_id=current_user.id
        ).all()
        
        if any(f.name == new_name and f.id != folder_id for f in same_parent_folders):
            return jsonify({
                'success': False,
                'message': '同一目录下已存在同名文件夹'
            })
        
        # 更新文件夹名
        folder.name = new_name
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件夹名修改成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/admin/logs')
@login_required
def view_logs():
    if not is_super_admin(current_user):
        flash('只有超级管理员和具有最高权限的用户可以访问此页面')
        return redirect(url_for('main.index'))
        
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = 20  # 减少每页显示量，避免载入过多数据
        action = request.args.get('action', '')
        user_id = request.args.get('user_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        search = request.args.get('search', '')
        
        current_app.logger.info(f'请求日志页面：页码={page}, 操作={action}, 用户={user_id}, 搜索={search}')
        
        # 构建查询
        query = UserLog.query.order_by(UserLog.timestamp.desc())
        
        # 应用过滤条件
        if action:
            query = query.filter(UserLog.action == action)
        if user_id:
            query = query.filter(UserLog.user_id == user_id)
        if start_date:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(UserLog.timestamp >= start)
            except Exception as e:
                current_app.logger.error(f'解析开始日期失败: {str(e)}')
        if end_date:
            try:
                end = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(UserLog.timestamp < end)
            except Exception as e:
                current_app.logger.error(f'解析结束日期失败: {str(e)}')
        if search:
            query = query.filter(UserLog.details.ilike(f'%{search}%'))
        
        # 手动分页实现，避免分页API问题
        try:
            total_count = query.count()
            current_app.logger.info(f'找到 {total_count} 条日志记录')
            
            # 计算分页
            max_page = (total_count + per_page - 1) // per_page if total_count > 0 else 1
            if page > max_page:
                page = 1  # 超出范围，返回第一页
                
            offset = (page - 1) * per_page
            logs = query.limit(per_page).offset(offset).all()
            
            # 创建简单分页对象
            class SimplePagination:
                def __init__(self, page, per_page, total):
                    self.page = page
                    self.per_page = per_page
                    self.total = total
                    self.pages = max_page
                    self.items = logs
                    
                def iter_pages(self):
                    for i in range(1, self.pages + 1):
                        if i == 1 or i == self.pages or abs(i - self.page) < 3:
                            yield i
                        elif abs(i - self.page) == 3:
                            yield None
            
            pagination = SimplePagination(page, per_page, total_count)
            
        except Exception as e:
            current_app.logger.error(f'分页失败: {str(e)}')
            logs = query.limit(per_page).all()  # 获取前20条不分页
            pagination = None
            
        # 获取所有用户，用于过滤
        users = User.query.all()
        
        # 获取所有操作类型
        try:
            actions = db.session.query(UserLog.action).distinct().all()
            actions = [action[0] for action in actions if action[0]]
        except Exception as e:
            current_app.logger.error(f'获取操作类型失败: {str(e)}')
            actions = []
        
        return render_template('admin/logs.html',
                             logs=logs,  # 直接传递日志列表
                             pagination=pagination,
                             users=users,
                             actions=actions,
                             current_user_id=user_id,
                             current_action=action,
                             start_date=start_date,
                             end_date=end_date,
                             search=search)
                             
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        current_app.logger.error(f'查看日志失败: {str(e)}\n{error_details}')
        flash(f'读取日志失败: {str(e)}')
        return redirect(url_for('main.index'))

@main.route('/folder/<int:folder_id>/permissions')
@login_required
def get_folder_permissions(folder_id):
    try:
        folder = Folder.query.get_or_404(folder_id)
        
        # 检查权限
        if not folder.can_modify(current_user):
            return jsonify({
                'success': False,
                'message': '您没有权限管理此文件夹'
            })
        
        return jsonify({
            'success': True,
            'is_public': folder.is_public,
            'read_only': folder.read_only,
            'allowed_users': folder.allowed_users
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/folder/<int:folder_id>/permissions', methods=['POST'])
@login_required
def update_folder_permissions(folder_id):
    try:
        folder = Folder.query.get_or_404(folder_id)
        
        # 检查权限 - 超级管理员或文件夹所有者可以修改权限
        if not is_super_admin(current_user) and folder.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '您没有权限修改此文件夹的设置'
            })
        
        # 获取JSON数据
        data = request.get_json()
        
        # 更新权限设置
        folder.is_public = bool(data.get('is_public', False))
        folder.read_only = bool(data.get('read_only', False))
        folder.allowed_users = data.get('allowed_users', '')
        
        db.session.commit()
        
        current_app.logger.info(
            f'用户 {current_user.username} 更新文件夹 {folder.name} 的权限设置: ' +
            f'公开={folder.is_public}, 只读={folder.read_only}, ' +
            f'授权用户={folder.allowed_users}'
        )
        
        # 记录日志
        log_user_action(current_user, 'MODIFY_PERMISSION', 
            f'修改文件夹 {folder.name} 权限: 公开={folder.is_public}, 只读={folder.read_only}')
        
        return jsonify({
            'success': True,
            'message': '权限设置已更新'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新文件夹权限失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/admin/user/<int:user_id>/folders')
@login_required
def get_user_folders(user_id):
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': '没有超级管理员权限'
        })
    
    try:
        user = User.query.get_or_404(user_id)
        
        # 获取用户的文件夹
        user_folders = [{
            'id': folder.id,
            'name': folder.name,
            'is_public': folder.is_public,
            'read_only': folder.read_only,
            'allowed_users': [int(id) for id in folder.allowed_users.split(',') if id]
        } for folder in user.folders]
        
        # 获取共享给用户的文件夹
        shared_folders = [{
            'id': folder.id,
            'name': folder.name,
            'owner_name': folder.owner.username,
            'is_public': folder.is_public,
            'read_only': folder.read_only
        } for folder in Folder.query.filter(
            Folder.user_id != user_id,
            db.or_(
                Folder.is_public == True,
                Folder.allowed_users.like(f'%{user_id}%')
            )
        ).all()]
        
        # 获取所有非管理员用户
        all_users = [{
            'id': u.id,
            'username': u.username
        } for u in User.query.filter_by(is_admin=False).all()]
        
        return jsonify({
            'success': True,
            'user_folders': user_folders,
            'shared_folders': shared_folders,
            'all_users': all_users
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/batch_download', methods=['POST'])
@login_required
def batch_download():
    try:
        data = request.get_json()
        file_ids = data.get('file_ids', [])
        folder_ids = data.get('folder_ids', [])
        
        # 如果只有一个文件且没有文件夹，使用单文件下载
        if len(file_ids) == 1 and not folder_ids:
            return redirect(url_for('main.download_file', file_id=file_ids[0]))
            
        # 多个文件或文件夹打包下载
        memory_file = io.BytesIO()
        with zipfile.ZipFile(memory_file, 'w') as zf:
            # 处理选中的文件
            if file_ids:
                files = File.query.filter(File.id.in_(file_ids)).all()
                for file in files:
                    # 检查权限
                    if not current_user.is_admin and file.user_id != current_user.id:
                        if not file.folder or not file.folder.can_access(current_user):
                            continue
                            
                    file_path = os.path.join(
                        current_app.config['UPLOAD_FOLDER'],
                        file.path,
                        file.filename
                    )
                    if os.path.exists(file_path):
                        zip_path = os.path.join(file.path, file.original_filename) if file.path else file.original_filename
                        zf.write(file_path, zip_path)
            
            # 处理选中的文件夹
            if folder_ids:
                folders = Folder.query.filter(Folder.id.in_(folder_ids)).all()
                for folder in folders:
                    # 检查权限
                    if not current_user.is_admin and folder.user_id != current_user.id:
                        if not folder.can_access(current_user):
                            continue
                            
                    # 递归添加文件夹中的所有文件
                    def add_folder_to_zip(current_folder, base_path=''):
                        # 添加当前文件夹中的文件
                        for file in current_folder.files:
                            file_path = os.path.join(
                                current_app.config['UPLOAD_FOLDER'],
                                file.path,
                                file.filename
                            )
                            if os.path.exists(file_path):
                                zip_path = os.path.join(base_path, current_folder.name, file.original_filename)
                                zf.write(file_path, zip_path)
                        
                        # 递归处理子文件夹
                        for subfolder in current_folder.subfolders:
                            if current_user.is_admin or subfolder.can_access(current_user):
                                add_folder_to_zip(subfolder, os.path.join(base_path, current_folder.name))
                    
                    add_folder_to_zip(folder)
        
        memory_file.seek(0)
        
        # 记录下载操作
        downloaded_items = []
        if file_ids:
            files = File.query.filter(File.id.in_(file_ids)).all()
            downloaded_items.extend(f.original_filename for f in files)
        if folder_ids:
            folders = Folder.query.filter(Folder.id.in_(folder_ids)).all()
            downloaded_items.extend(f.name for f in folders)
            
        log_user_action(
            current_user,
            'BATCH_DOWNLOAD',
            f'批量下载: {", ".join(downloaded_items)}'
        )
        
        return send_file(
            memory_file,
            mimetype='application/zip',
            as_attachment=True,
            download_name='download.zip'
        )
        
    except Exception as e:
        current_app.logger.error(f'批量下载失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '下载失败'
        }), 500 

@main.route('/file/move/<int:file_id>', methods=['POST'])
@login_required
def move_file(file_id):
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': '没有权限执行此操作'
        })
        
    try:
        file = File.query.get_or_404(file_id)
        data = request.get_json()
        new_folder_id = data.get('folder_id')
        
        # 如果是移动到根目录
        if new_folder_id == 0:
            file.folder_id = None
            new_location = "根目录"
        else:
            # 检查目标文件夹是否存在
            new_folder = Folder.query.get_or_404(new_folder_id)
            file.folder_id = new_folder_id
            new_location = new_folder.name
            
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'MOVE_FILE',
            f'移动文件 {file.original_filename} 到 {new_location}'
        )
        
        return jsonify({
            'success': True,
            'message': '文件移动成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'移动文件失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '移动文件失败'
        })

# 获取所有文件夹列表（用于移动文件时选择目标文件夹）
@main.route('/folders/list')
@login_required
def get_folders_list():
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': '没有权限执行此操作'
        })
        
    try:
        folders = Folder.query.all()
        folder_list = [{
            'id': folder.id,
            'name': folder.name,
            'parent_id': folder.parent_id
        } for folder in folders]
        
        return jsonify({
            'success': True,
            'folders': folder_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

# 回收站列表
@main.route('/trash')
@login_required
def trash():
    # 管理员可以看到所有删除的内容，普通用户只能看到自己的
    if is_super_admin(current_user):
        files = File.query.filter_by(is_deleted=True).all()
        folders = Folder.query.filter_by(is_deleted=True).all()
    else:
        files = File.query.filter_by(
            is_deleted=True,
            user_id=current_user.id
        ).all()
        folders = Folder.query.filter_by(
            is_deleted=True,
            user_id=current_user.id
        ).all()
    return render_template('trash.html', files=files, folders=folders)

# 恢复文件
@main.route('/restore_file/<int:file_id>', methods=['POST'])
@login_required
def restore_file(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user) and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限恢复此文件'
            })
            
        file.is_deleted = False
        file.delete_time = None
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'RESTORE_FILE',
            f'从回收站恢复文件: {file.original_filename}'
        )
        
        return jsonify({
            'success': True,
            'message': '文件已恢复'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'恢复文件失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '恢复失败'
        })

# 永久删除文件
@main.route('/permanent_delete/<int:file_id>', methods=['POST'])
@login_required
def permanent_delete(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user) and file.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限删除此文件'
            })
            
        # 删除物理文件
        file_path = os.path.join(
            current_app.config['UPLOAD_FOLDER'],
            file.path,
            file.filename
        )
        if os.path.exists(file_path):
            os.remove(file_path)
            
        # 记录操作日志
        log_user_action(
            current_user,
            'PERMANENT_DELETE',
            f'永久删除文件: {file.original_filename}'
        )
            
        # 删除数据库记录
        db.session.delete(file)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件已永久删除'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'永久删除文件失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除失败'
        })

# 添加恢复文件夹的路由
@main.route('/restore_folder/<int:folder_id>', methods=['POST'])
@login_required
def restore_folder(folder_id):
    try:
        folder = Folder.query.get_or_404(folder_id)
        
        # 检查权限
        if not is_super_admin(current_user) and folder.user_id != current_user.id:
            return jsonify({
                'success': False,
                'message': '没有权限恢复此文件夹'
            })
            
        # 递归恢复文件夹及其内容
        def restore_from_trash(current_folder):
            current_app.logger.info(f'恢复文件夹: {current_folder.name}')
            
            # 恢复当前文件夹
            current_folder.is_deleted = False
            current_folder.delete_time = None
            
            # 如果有保存的路径信息，恢复到原位置
            if current_folder.path:
                parent_id = int(current_folder.path) if current_folder.path.isdigit() else None
                current_folder.parent_id = parent_id
                current_folder.path = ''
            
            # 恢复文件夹中的文件
            for file in current_folder.files:
                file.is_deleted = False
                file.delete_time = None
                current_app.logger.info(f'恢复文件: {file.original_filename}')
            
            # 递归恢复子文件夹
            for subfolder in current_folder.subfolders:
                restore_from_trash(subfolder)
        
        # 执行恢复
        restore_from_trash(folder)
        db.session.commit()
        current_app.logger.info('恢复成功')
        
        # 记录操作日志
        log_user_action(
            current_user,
            'RESTORE_FOLDER',
            f'从回收站恢复文件夹: {folder.name}'
        )
        
        return jsonify({
            'success': True,
            'message': '文件夹已恢复'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'恢复文件夹失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '恢复失败'
        })

# 定时清理回收站中超过1个月的文件
def cleanup_trash():
    try:
        one_month_ago = datetime.now() - timedelta(days=30)
        expired_files = File.query.filter(
            File.is_deleted == True,
            File.delete_time <= one_month_ago
        ).all()
        
        for file in expired_files:
            try:
                # 删除物理文件
                file_path = os.path.join(
                    current_app.config['UPLOAD_FOLDER'],
                    file.path,
                    file.filename
                )
                if os.path.exists(file_path):
                    os.remove(file_path)
                    
                # 删除数据库记录
                db.session.delete(file)
                
                current_app.logger.info(f'自动清理过期文件: {file.original_filename}')
                
            except Exception as e:
                current_app.logger.error(f'清理文件失败: {str(e)}')
                continue
                
        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'清理回收站失败: {str(e)}') 

# 添加清空回收站的路由
@main.route('/empty_trash', methods=['POST'])
@login_required
def empty_trash():
    try:
        if not current_user.is_admin:
            return jsonify({
                'success': False,
                'message': '只有管理员可以清空回收站'
            })
            
        # 获取所有已删除的文件和文件夹
        deleted_files = File.query.filter_by(is_deleted=True).all()
        deleted_folders = Folder.query.filter_by(is_deleted=True).all()
        
        # 删除物理文件
        for file in deleted_files:
            try:
                file_path = os.path.join(
                    current_app.config['UPLOAD_FOLDER'],
                    file.path,
                    file.filename
                )
                if os.path.exists(file_path):
                    os.remove(file_path)
                db.session.delete(file)
            except Exception as e:
                current_app.logger.error(f'删除文件失败: {str(e)}')
                continue
        
        # 删除文件夹记录
        for folder in deleted_folders:
            try:
                db.session.delete(folder)
            except Exception as e:
                current_app.logger.error(f'删除文件夹失败: {str(e)}')
                continue
        
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'EMPTY_TRASH',
            f'清空回收站'
        )
        
        return jsonify({
            'success': True,
            'message': '回收站已清空'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'清空回收站失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '清空回收站失败'
        })

@main.route('/admin/keywords', methods=['GET', 'POST'])
@login_required
def manage_keywords():
    """关键字管理页面"""
    if not can_access_feature(current_user, 'keyword_management'):
        flash(get_text('You do not have permission to access this feature'), 'warning')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        word = request.form.get('keyword')
        keyword_type = request.form.get('type')
        folder_id = request.form.get('folder_id')
        
        # 验证参数
        if not word:
            flash(get_text('Keyword cannot be empty'), 'danger')
            return redirect(url_for('main.manage_keywords'))
            
        # 处理folder_id
        if folder_id:
            try:
                folder_id = int(folder_id)
                # 验证文件夹是否存在
                folder = Folder.query.get(folder_id)
                if not folder:
                    flash(get_text('Selected folder does not exist'), 'danger')
                    return redirect(url_for('main.manage_keywords'))
            except ValueError:
                folder_id = None
        else:
            folder_id = None
            
        # 检查关键字是否已存在（同一文件夹内）
        existing = Keyword.query.filter_by(
            word=word, 
            keyword_type=int(keyword_type),
            folder_id=folder_id
        ).first()
        
        if existing:
            # 根据类型显示不同的提示信息
            if int(keyword_type) == 1:
                flash(get_text('This keyword 1 already exists for the selected folder'), 'warning')
            else:
                flash(get_text('This keyword 2 already exists for the selected folder'), 'warning')
        else:
            # 创建关键字
            keyword = Keyword(
                word=word,
                keyword_type=int(keyword_type) if keyword_type else 0,
                created_by=current_user.id,
                folder_id=folder_id
            )
            
            db.session.add(keyword)
            db.session.commit()
            
            # 记录操作日志
            folder_info = f" (关联文件夹: {folder.name})" if folder_id else " (全局)"
            keyword_type_str = "关键字1" if int(keyword_type) == 1 else "关键字2"
            log_user_action(
                current_user,
                'CREATE_KEYWORD',
                f'创建{keyword_type_str}: {word}{folder_info}'
            )
            
            # 根据类型显示不同的成功信息
            if int(keyword_type) == 1:
                flash(get_text('Keyword 1 added successfully'), 'success')
            else:
                flash(get_text('Keyword 2 added successfully'), 'success')
    
    # 获取所有关键字，关联文件夹信息
    keywords = Keyword.query.options(db.joinedload(Keyword.folder)).order_by(Keyword.keyword_type, Keyword.word).all()
    
    # 获取文件夹列表（用于关联）
    # 仅获取用户有权限的文件夹
    if is_super_admin(current_user):
        # 超级管理员可以看到所有文件夹
        folders = Folder.query.filter_by(is_deleted=False).all()
    elif current_user.is_admin:
        # 管理员可以看到所有非删除的文件夹
        folders = Folder.query.filter_by(is_deleted=False).all()
    else:
        # 普通用户只能看到自己的文件夹和共享给自己的文件夹
        my_folders = Folder.query.filter_by(user_id=current_user.id, is_deleted=False).all()
        shared_folder_ids = []
        
        # 获取共享文件夹ID
        for folder in Folder.query.filter(Folder.is_deleted==False).all():
            if folder.can_access(current_user):
                shared_folder_ids.append(folder.id)
                
        shared_folders = Folder.query.filter(Folder.id.in_(shared_folder_ids)).all() if shared_folder_ids else []
        folders = my_folders + [f for f in shared_folders if f.id not in [folder.id for folder in my_folders]]
    
    return render_template('keyword_management.html', keywords=keywords, folders=folders)

@main.route('/admin/keywords/delete/<int:keyword_id>', methods=['POST'])
@login_required
def delete_keyword(keyword_id):
    """删除关键字"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('You do not have permission to perform this action')
        })
        
    try:
        keyword = Keyword.query.get_or_404(keyword_id)
        db.session.delete(keyword)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'DELETE_KEYWORD',
            f'删除关键字: {keyword.word} (类型: {keyword.keyword_type})'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('Keyword deleted successfully')
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/keywords')
@login_required
def get_keywords():
    """获取关键字列表
    
    参数:
        folder_id: 文件夹ID（可选）
        type: 关键字类型（可选，1=关键字1, 2=关键字2）
        include_global: 是否包含全局关键字（可选，默认true）
    """
    try:
        folder_id = request.args.get('folder_id', '')
        keyword_type = request.args.get('type', '')
        include_global = request.args.get('include_global', 'true').lower() == 'true'
        
        current_app.logger.debug(f'关键字API请求: folder_id={folder_id}, type={keyword_type}, include_global={include_global}')
        
        # 构建查询
        query = Keyword.query
        
        # 按文件夹筛选
        if folder_id:
            try:
                folder_id_int = int(folder_id)
                
                if include_global:
                    # 包含全局关键字和特定文件夹关键字
                    query = query.filter(db.or_(
                        Keyword.folder_id == folder_id_int,
                        Keyword.folder_id == None
                    ))
                else:
                    # 只包含特定文件夹关键字
                    query = query.filter(Keyword.folder_id == folder_id_int)
            except ValueError:
                # 文件夹ID无效时只返回全局关键字
                query = query.filter(Keyword.folder_id == None)
        else:
            # 没有指定文件夹，返回全局关键字（folder_id为空）
            query = query.filter(Keyword.folder_id == None)
            
        # 按类型筛选
        if keyword_type:
            try:
                k_type = int(keyword_type)
                query = query.filter(Keyword.keyword_type == k_type)
            except ValueError:
                pass
                
        # 获取结果并排序
        keywords = query.order_by(Keyword.word).all()
        keyword_list = [{'id': k.id, 'word': k.word, 'folder_id': k.folder_id} for k in keywords]
        
        # 去重（可能同时有全局和文件夹特定的相同关键字）
        if include_global and folder_id:
            # 创建一个词到ID的映射，优先使用文件夹特定的关键字
            word_map = {}
            for k in keyword_list:
                word = k['word']
                if word not in word_map or k['folder_id'] is not None:
                    word_map[word] = k
            
            # 重建去重后的列表
            keyword_list = list(word_map.values())
            
        current_app.logger.debug(f'返回关键字数量: {len(keyword_list)}')
        
        return jsonify({
            'success': True,
            'keywords': keyword_list
        })
    except Exception as e:
        current_app.logger.error(f'获取关键字失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e),
            'keywords': []
        })

# 辅助函数：获取所有子文件夹的ID
def get_all_subfolder_ids(folder_id):
    """获取指定文件夹及其所有子文件夹的ID列表"""
    result = [folder_id]
    
    # 递归获取所有子文件夹
    def get_subfolders(parent_id):
        subfolders = Folder.query.filter_by(parent_id=parent_id, is_deleted=False).all()
        for subfolder in subfolders:
            result.append(subfolder.id)
            get_subfolders(subfolder.id)
    
    get_subfolders(folder_id)
    return result

@main.route('/admin/users/<int:user_id>/reset_password', methods=['GET', 'POST'])
@login_required
def admin_reset_password(user_id):
    # 将检查从is_admin改为检查用户名是否为'cv24051'
    if current_user.username != 'cv24051':
        flash('只有超级管理员(cv24051)可以重置用户密码')
        return redirect(url_for('main.admin_users'))
    
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if new_password != confirm_password:
            flash('两次输入的新密码不一致')
            return redirect(url_for('main.admin_reset_password', user_id=user_id))
            
        user.set_password(new_password)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user,
            'RESET_PASSWORD',
            f'管理员修改用户密码: {user.username}'
        )
        
        flash(f'用户 {user.username} 的密码已成功修改')
        return redirect(url_for('main.admin_users'))
        
    return render_template('admin/reset_password.html', user=user)

@main.route('/api/chunk-upload', methods=['POST'])
@login_required
def chunk_upload():
    """处理分片上传请求，支持大文件断点续传"""
    try:
        # 获取请求参数
        chunk_number = int(request.form.get('chunkNumber'))
        total_chunks = int(request.form.get('totalChunks'))
        file_id = request.form.get('fileId')
        original_filename = request.form.get('filename')
        folder_id = request.form.get('folderId')
        folder_id = int(folder_id) if folder_id and folder_id != 'null' else None
        
        # 验证参数
        if not file_id or not original_filename:
            return jsonify({'success': False, 'message': '缺少必要参数'})
            
        # 检查权限
        if folder_id:
            folder = Folder.query.get_or_404(folder_id)
            if not is_super_admin(current_user) and not folder.can_modify(current_user):
                return jsonify({'success': False, 'message': '没有权限上传到此文件夹'})
        
        # 确定文件存储位置
        temp_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'temp', current_user.username, file_id)
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
            
        # 保存当前分片
        chunk_file = request.files.get('file')
        if not chunk_file:
            return jsonify({'success': False, 'message': '未找到文件数据'})
            
        chunk_path = os.path.join(temp_dir, f'chunk_{chunk_number}')
        chunk_file.save(chunk_path)
        
        # 检查是否为最后一个分片
        if chunk_number == total_chunks - 1:
            # 合并所有分片
            final_filename = secure_filename(original_filename)
            base, ext = os.path.splitext(final_filename)
            unique_filename = f"{base}_{int(time.time())}{ext}"
            
            # 确定最终存储路径
            if folder_id:
                folder = Folder.query.get(folder_id)
                relative_path = folder.path
            else:
                relative_path = os.path.join('user_' + str(current_user.id))
                
            storage_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
            if not os.path.exists(storage_path):
                os.makedirs(storage_path)
                
            final_path = os.path.join(storage_path, unique_filename)
            
            # 合并所有分片文件
            with open(final_path, 'wb') as f_out:
                for i in range(total_chunks):
                    chunk_path = os.path.join(temp_dir, f'chunk_{i}')
                    if os.path.exists(chunk_path):
                        with open(chunk_path, 'rb') as f_in:
                            f_out.write(f_in.read())
                            
            # 获取文件类型和大小
            file_type = chunk_file.content_type or 'application/octet-stream'
            file_size = os.path.getsize(final_path)
            
            # 创建文件记录
            new_file = File(
                filename=unique_filename,
                original_filename=original_filename,
                file_type=file_type,
                file_size=file_size,
                path=relative_path,
                folder_id=folder_id,
                user_id=current_user.id
            )
            
            db.session.add(new_file)
            db.session.commit()
            
            # 记录操作日志
            folder_name = folder.name if folder_id else "根目录"
            log_user_action(
                current_user,
                'UPLOAD',
                f'上传文件到 {folder_name}: {original_filename} (分片上传)'
            )
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return jsonify({
                'success': True, 
                'message': '文件上传成功',
                'fileId': new_file.id
            })
        
        # 如果不是最后一个分片，返回成功
        return jsonify({
            'success': True,
            'message': f'分片 {chunk_number+1}/{total_chunks} 上传成功',
            'chunkNumber': chunk_number
        })
            
    except Exception as e:
        current_app.logger.error(f'分片上传失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'上传失败: {str(e)}'
        })

# 设置语言
@main.route('/set_language/<lang>', methods=['POST'])
def set_language(lang):
    """设置用户界面语言"""
    if lang in current_app.config['SUPPORTED_LANGUAGES']:
        session['language'] = lang
        return jsonify({
            'success': True,
            'message': get_text('language_changed', lang)
        })
    else:
        return jsonify({
            'success': False,
            'message': get_text('Unsupported language', lang)
        })

@main.route('/manage_groups')
@login_required
def manage_groups():
    """用户组管理页面"""
    # 允许高级用户和超级管理员访问
    if not current_user.is_admin and not is_super_admin(current_user):
        return redirect(url_for('main.index'))
    
    groups = UserGroup.query.all()
    users = User.query.all()
    return render_template('manage_groups.html', groups=groups, users=users, t=get_text)

@main.route('/api/groups', methods=['GET'])
@login_required
def get_groups():
    """获取所有用户组"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('Permission denied')
        })
        
    try:
        groups = UserGroup.query.all()
        groups_data = []
        
        for group in groups:
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'member_count': len(group.members),
                'can_upload': group.can_upload,
                'can_download': group.can_download,
                'can_delete': group.can_delete,
                'can_share': group.can_share,
                'storage_limit': group.storage_limit,
                'created_by': group.created_by,
                'create_date': group.create_date.strftime('%Y-%m-%d %H:%M:%S') if group.create_date else ''
            }
            groups_data.append(group_data)
            
        return jsonify({
            'success': True,
            'groups': groups_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/groups/<int:group_id>', methods=['PUT'])
@login_required
def update_group(group_id):
    """更新用户组信息"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('No super administrator privileges')
        })
    
    try:
        group = UserGroup.query.get_or_404(group_id)
        data = request.get_json()
        
        # 更新组信息
        if 'name' in data and data['name'].strip():
            # 检查新名称是否与其他组冲突
            existing = UserGroup.query.filter(UserGroup.name == data['name'].strip(), 
                                             UserGroup.id != group_id).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': get_text('Group with same name already exists')
                })
            group.name = data['name'].strip()
            
        if 'description' in data:
            group.description = data['description']
            
        if 'can_upload' in data:
            group.can_upload = data['can_upload']
            
        if 'can_download' in data:
            group.can_download = data['can_download']
            
        if 'can_delete' in data:
            group.can_delete = data['can_delete']
            
        if 'can_share' in data:
            group.can_share = data['can_share']
            
        if 'storage_limit' in data:
            group.storage_limit = data['storage_limit']
            
        if 'is_admin_group' in data:
            group.is_admin_group = data['is_admin_group']
        
        # 更新成员
        if 'member_ids' in data:
            # 清除现有成员
            group.members = []
            
            # 添加新成员
            for user_id in data['member_ids']:
                user = User.query.get(user_id)
                if user:
                    group.members.append(user)
        
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user, 
            'UPDATE_GROUP', 
            f'{get_text("Updated user group")}: {group.name}',
            group.id,
            'group'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('User group updated successfully')
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'{get_text("Failed to update user group")}: {str(e)}'
        })

@main.route('/api/groups/<int:group_id>', methods=['DELETE'])
@login_required
def delete_group(group_id):
    """删除用户组"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('No super administrator privileges')
        })
    
    try:
        group = UserGroup.query.get_or_404(group_id)
        group_name = group.name
        
        # 删除组
        db.session.delete(group)
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user, 
            'DELETE_GROUP', 
            f'{get_text("Deleted user group")}: {group_name}',
            group_id,
            'group'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('User group deleted successfully')
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'{get_text("Failed to delete user group")}: {str(e)}'
        })

@main.route('/api/groups/<int:group_id>/members', methods=['GET'])
@login_required
def get_group_members(group_id):
    """获取用户组成员"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('Permission denied')
        })
        
    try:
        group = UserGroup.query.get_or_404(group_id)
        members = [{'id': user.id, 'username': user.username} for user in group.members]
        
        return jsonify({
            'success': True,
            'members': members
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/groups/<int:group_id>/members', methods=['PUT'])
@login_required
def update_group_members(group_id):
    """更新用户组成员"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('Permission denied')
        })
        
    try:
        group = UserGroup.query.get_or_404(group_id)
        data = request.get_json()
        
        # 更新成员
        member_ids = data.get('member_ids', [])
        users = User.query.filter(User.id.in_(member_ids)).all()
        group.members = users
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': get_text('Group members updated successfully')
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/groups/<int:group_id>/members', methods=['POST'])
@login_required
def add_group_member(group_id):
    """添加用户组成员"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('No super administrator privileges')
        })
    
    try:
        group = UserGroup.query.get_or_404(group_id)
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({
                'success': False,
                'message': get_text('User ID cannot be empty')
            })
        
        user = User.query.get_or_404(user_id)
        
        # 检查用户是否已在组中
        if user in group.members:
            return jsonify({
                'success': False,
                'message': get_text('User {username} is already a member of this group').format(username=user.username)
            })
        
        # 添加用户到组
        group.members.append(user)
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user, 
            'ADD_GROUP_MEMBER', 
            f'{get_text("Added user {username} to group {groupname}").format(username=user.username, groupname=group.name)}',
            group.id,
            'group'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('User {username} has been added to the group').format(username=user.username)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/groups/<int:group_id>/members/<int:user_id>', methods=['DELETE'])
@login_required
def remove_group_member(group_id, user_id):
    """从用户组中移除成员"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('No super administrator privileges')
        })
    
    try:
        group = UserGroup.query.get_or_404(group_id)
        user = User.query.get_or_404(user_id)
        
        # 检查用户是否在组中
        if user not in group.members:
            return jsonify({
                'success': False,
                'message': get_text('User {username} is not a member of this group').format(username=user.username)
            })
        
        # 从组中移除用户
        group.members.remove(user)
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user, 
            'REMOVE_GROUP_MEMBER', 
            get_text('Removed user {username} from group {groupname}').format(username=user.username, groupname=group.name),
            group.id,
            'group'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('User {username} has been removed from the group').format(username=user.username)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/get_folder_permissions/<int:folder_id>', methods=['GET'])
def get_folder_permissions_api(folder_id):
    folder = Folder.query.get_or_404(folder_id)
    
    # 检查用户权限 - 允许文件夹所有者、超级管理员和普通管理员
    if not (is_owner(current_user, folder) or 
            is_super_admin(current_user) or 
            current_user.is_admin or
            current_user.username == 'cv24051'):
        return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
    
    # 获取所有用户（不排除当前用户，这样管理员也能设置权限给自己）
    users = User.query.all()
    
    # 获取所有用户组
    groups = UserGroup.query.all()
    
    # 将允许的用户ID转换为整数列表
    allowed_users = []
    if folder.allowed_users:
        allowed_users = [int(user_id) for user_id in folder.allowed_users.split(',') if user_id]
    
    # 获取允许的用户组ID列表
    allowed_groups = []
    if folder.allowed_groups:
        allowed_groups = [int(group_id) for group_id in folder.allowed_groups.split(',') if group_id]
    
    folder_data = {
        'id': folder.id,
        'name': folder.name,
        'is_public': folder.is_public,
        'read_only': folder.read_only,
        'allowed_users': allowed_users,
        'allowed_groups': allowed_groups
    }
    
    return jsonify({
        'success': True, 
        'folder': folder_data,
        'users': [{'id': user.id, 'username': user.username} for user in users],
        'groups': [{'id': group.id, 'name': group.name} for group in groups]
    })

@main.route('/update_folder_permissions', methods=['POST'])
def update_folder_permissions_api():
    folder_id = request.form.get('folder_id')
    
    if not folder_id:
        return jsonify({'success': False, 'message': t('Missing folder ID')})
    
    folder = Folder.query.get_or_404(int(folder_id))
    
    # 检查用户权限
    if not (is_owner(current_user, folder) or 
            is_super_admin(current_user) or 
            current_user.username == 'cv24051'):
        return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
    
    # 更新文件夹权限
    folder.is_public = request.form.get('is_public') == 'true'
    folder.read_only = request.form.get('read_only') == 'true'
    
    # 更新允许的用户
    allowed_users = request.form.getlist('allowed_users[]')
    folder.allowed_users = ','.join(allowed_users) if allowed_users else ''
    
    # 更新允许的用户组
    allowed_groups = request.form.getlist('allowed_groups[]')
    folder.allowed_groups = ','.join(allowed_groups) if allowed_groups else ''
    
    # 保存更改
    db.session.commit()
    
    # 记录操作日志
    log_user_action(
        current_user,
        'UPDATE_PERMISSIONS',
        f'更新文件夹权限: {folder.name}'
    )
    
    # 使用get_text函数替代未定义的t函数
    return jsonify({'success': True, 'message': get_text('Folder permissions updated successfully')})

@main.route('/api/users/<int:user_id>/groups', methods=['GET'])
@login_required
def get_user_groups(user_id):
    """获取用户所在的用户组和所有可用的用户组"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': '没有足够的权限'
        })
    
    try:
        user = User.query.get_or_404(user_id)
        
        # 获取用户所在的所有组
        user_groups = [group.id for group in user.groups]
        
        # 获取所有可用的用户组
        all_groups = UserGroup.query.all()
        groups_data = [{'id': group.id, 'name': group.name} for group in all_groups]
        
        return jsonify({
            'success': True,
            'user_groups': user_groups,
            'groups': groups_data
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/api/users/<int:user_id>/groups', methods=['PUT'])
@login_required
def update_user_groups(user_id):
    """更新用户所在的用户组"""
    if not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': '没有足够的权限'
        })
    
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        group_ids = data.get('group_ids', [])
        
        # 清除当前用户的所有组
        user.groups = []
        
        # 添加用户到指定的组
        for group_id in group_ids:
            group = UserGroup.query.get(group_id)
            if group:
                user.groups.append(group)
        
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user, 
            'UPDATE_USER_GROUPS', 
            f'更新用户 {user.username} 的用户组',
            user.id,
            'user'
        )
        
        return jsonify({
            'success': True,
            'message': f'用户 {user.username} 的用户组已更新'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@main.route('/system_logs')
@login_required
def system_logs():
    """系统日志页面"""
    if not can_access_feature(current_user, 'system_logs'):
        flash(t('You do not have permission to access this feature'), 'warning')
        return redirect(url_for('main.index'))
    
    return render_template('system_logs.html')

@main.route('/keyword_management')
@login_required
def keyword_management():
    """关键字管理页面"""
    # 允许高级用户和超级管理员访问
    if not current_user.is_admin and not is_super_admin(current_user):
        return redirect(url_for('main.index'))
    
    return render_template('keyword_management.html')

@main.route('/api/groups', methods=['POST'])
@login_required
def create_group():
    """创建新用户组"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('Permission denied')
        })
    
    try:
        # 获取表单数据
        data = request.json
        name = data.get('name', '').strip()
        description = data.get('description', '')
        can_upload = data.get('can_upload', True)
        can_download = data.get('can_download', True)
        can_delete = data.get('can_delete', True)
        can_share = data.get('can_share', True)
        storage_limit = data.get('storage_limit', 0)
        is_admin_group = data.get('is_admin_group', False)
        
        # 验证数据
        if not name:
            return jsonify({
                'success': False,
                'message': get_text('Group name cannot be empty')
            })
        
        # 检查是否已存在同名组
        if UserGroup.query.filter_by(name=name).first():
            return jsonify({
                'success': False,
                'message': get_text('Group with same name already exists')
            })
        
        # 创建新组
        new_group = UserGroup(
            name=name,
            description=description,
            created_by=current_user.id,
            can_upload=can_upload,
            can_download=can_download,
            can_delete=can_delete,
            can_share=can_share,
            storage_limit=storage_limit,
            is_admin_group=is_admin_group
        )
        
        db.session.add(new_group)
        db.session.commit()
        
        # 记录日志
        log_user_action(
            current_user,
            'CREATE_GROUP',
            f'创建用户组: {name}'
        )
        
        return jsonify({
            'success': True,
            'message': get_text('User group created successfully'),
            'group_id': new_group.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'{get_text("Failed to create user group")}: {str(e)}'
        })

@main.route('/api/groups/<int:group_id>', methods=['GET'])
@login_required
def get_group(group_id):
    """获取单个用户组信息"""
    if not current_user.is_admin and not is_super_admin(current_user):
        return jsonify({
            'success': False,
            'message': get_text('Permission denied')
        })
        
    try:
        group = UserGroup.query.get_or_404(group_id)
        
        group_data = {
            'id': group.id,
            'name': group.name,
            'description': group.description,
            'can_upload': group.can_upload,
            'can_download': group.can_download,
            'can_delete': group.can_delete,
            'can_share': group.can_share,
            'is_admin_group': group.is_admin_group,
            'storage_limit': group.storage_limit,
            'created_by': group.created_by,
            'create_date': group.create_date.strftime('%Y-%m-%d %H:%M:%S') if group.create_date else ''
        }
        
        return jsonify({
            'success': True,
            'group': group_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

# 在文件中添加电子签名
@main.route('/file/signature/<int:file_id>')
@login_required
def file_signature(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限为此文件添加签名')
                return redirect(url_for('main.index'))
        
        # 获取文件已有的所有签名
        try:
            # 尝试使用file_id查询签名
            signatures = Signature.query.filter_by(file_id=file.id).all()
        except Exception as e:
            current_app.logger.error(f'查询签名失败，可能是数据库结构问题: {str(e)}')
            # 如果查询失败（可能是缺少file_id字段），返回空列表
            signatures = []
            # 显示警告消息
            flash('数据库结构需要更新，签名功能可能无法正常工作。请联系管理员。', 'warning')
        
        # 获取用户的默认签名
        try:
            user_signatures = UserSignature.query.filter_by(user_id=current_user.id).all()
        except Exception:
            # 如果查询失败，返回空列表
            user_signatures = []
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'VIEW_SIGNATURE', 
            f'查看文件签名页面: {file.original_filename}',
            file.id,
            'file'
        )
        
        return render_template('signature.html', 
                              file=file,
                              signatures=signatures,
                              user_signatures=user_signatures)
    except Exception as e:
        current_app.logger.error(f'访问签名页面失败: {str(e)}')
        flash('访问签名页面失败')
        return redirect(url_for('main.index'))

# 添加签名API
@main.route('/file/add_signature/<int:file_id>', methods=['POST'])
@login_required
def add_signature(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                return jsonify({'success': False, 'message': '您没有权限为此文件添加签名'})
        
        # 获取签名数据
        signature_data = request.form.get('signature_data')
        metadata = request.form.get('metadata', '{}')
        user_signature_id = request.form.get('user_signature_id')
        
        # 如果提供了用户签名ID，则使用该签名
        if user_signature_id:
            user_signature = UserSignature.query.get(user_signature_id)
            if user_signature and user_signature.user_id == current_user.id:
                signature_data = user_signature.signature_data
        
        if not signature_data:
            return jsonify({'success': False, 'message': '签名数据不能为空'})
        
        try:
            # 创建签名记录
            signature = Signature(
                user_id=current_user.id,
                file_id=file.id,
                signature_data=signature_data,
                signature_metadata=metadata
            )
            
            db.session.add(signature)
            db.session.commit()
            
            # 记录操作日志
            log_user_action(
                current_user, 
                'ADD_SIGNATURE', 
                f'为文件添加电子签名: {file.original_filename}',
                file.id,
                'file'
            )
            
            return jsonify({'success': True, 'message': '签名保存成功', 'signature_id': signature.id})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'保存签名失败，可能是数据库结构问题: {str(e)}')
            
            # 检查是否是数据库结构问题
            if "Unknown column 'signature.file_id'" in str(e):
                return jsonify({
                    'success': False, 
                    'message': '数据库结构需要更新，签名功能暂时无法使用。请联系管理员。',
                    'error_type': 'db_structure'
                })
            
            return jsonify({'success': False, 'message': f'保存签名失败: {str(e)}'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'保存签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'保存签名失败: {str(e)}'})

# 验证签名API
@main.route('/file/verify_signature/<int:signature_id>')
@login_required
def verify_signature(signature_id):
    try:
        try:
            signature = Signature.query.get_or_404(signature_id)
        except Exception as e:
            current_app.logger.error(f'获取签名失败，可能是数据库结构问题: {str(e)}')
            return jsonify({'success': False, 'message': '数据库结构需要更新，无法验证签名。请联系管理员。'})
        
        # 检查权限
        try:
            if not is_super_admin(current_user):
                if not signature.file.folder or not signature.file.folder.can_access(current_user):
                    return jsonify({'success': False, 'message': '您没有权限验证此签名'})
        except Exception as e:
            current_app.logger.error(f'检查签名权限失败: {str(e)}')
            # 如果无法检查权限，默认允许验证
        
        # 获取签名元数据
        metadata = {}
        if signature.signature_metadata:
            try:
                metadata = json.loads(signature.signature_metadata)
            except:
                metadata = {}
        
        # 构建验证信息
        verification_info = {
            'success': True,
            'signature': {
                'id': signature.id,
                'user': signature.user.username,
                'date': signature.signature_date.strftime('%Y年%m月%d日 %H:%M:%S'),
                'file': signature.file.original_filename if hasattr(signature, 'file') else '未知文件',
                'metadata': metadata
            }
        }
        
        # 记录操作日志
        try:
            log_user_action(
                current_user, 
                'VERIFY_SIGNATURE', 
                f'验证文件签名: {signature.file.original_filename if hasattr(signature, "file") else "未知文件"}',
                signature.file.id if hasattr(signature, "file") else 0,
                'file'
            )
        except Exception as e:
            current_app.logger.error(f'记录验证签名日志失败: {str(e)}')
        
        return jsonify(verification_info)
    except Exception as e:
        current_app.logger.error(f'验证签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'验证签名失败: {str(e)}'})

# 删除签名API
@main.route('/file/delete_signature/<int:signature_id>', methods=['POST'])
@login_required
def delete_signature(signature_id):
    try:
        try:
            signature = Signature.query.get_or_404(signature_id)
        except Exception as e:
            current_app.logger.error(f'获取签名失败，可能是数据库结构问题: {str(e)}')
            return jsonify({'success': False, 'message': '数据库结构需要更新，无法删除签名。请联系管理员。'})
        
        # 检查权限 - 只有管理员或签名创建者可以删除
        if not is_super_admin(current_user) and signature.user_id != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限删除此签名'})
        
        # 获取文件信息（安全获取，以防file_id字段不存在）
        try:
            file_id = signature.file_id
            file_name = signature.file.original_filename
        except Exception:
            file_id = 0
            file_name = "未知文件"
        
        # 删除签名
        db.session.delete(signature)
        db.session.commit()
        
        # 记录操作日志
        try:
            log_user_action(
                current_user, 
                'DELETE_SIGNATURE', 
                f'删除文件签名: {file_name}',
                file_id,
                'file'
            )
        except Exception as e:
            current_app.logger.error(f'记录删除签名日志失败: {str(e)}')
        
        return jsonify({'success': True, 'message': '签名已删除'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'删除签名失败: {str(e)}'})

# 用户签名管理页面
@main.route('/user/signatures')
@login_required
def user_signatures():
    try:
        # 获取用户的所有签名
        signatures = UserSignature.query.filter_by(user_id=current_user.id).all()
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'VIEW_USER_SIGNATURES', 
            f'查看个人签名管理页面',
            current_user.id,
            'user'
        )
        
        return render_template('user_signatures.html', signatures=signatures)
    except Exception as e:
        current_app.logger.error(f'访问用户签名页面失败: {str(e)}')
        flash('访问用户签名页面失败')
        return redirect(url_for('main.index'))

# 添加用户默认签名
@main.route('/user/add_signature', methods=['POST'])
@login_required
def add_user_signature():
    try:
        # 获取签名数据
        signature_data = request.form.get('signature_data')
        description = request.form.get('description', '')
        set_as_default = request.form.get('set_as_default') == 'true'
        
        if not signature_data:
            return jsonify({'success': False, 'message': '签名数据不能为空'})
        
        # 如果设置为默认，先将所有其他签名设为非默认
        if set_as_default:
            UserSignature.query.filter_by(user_id=current_user.id).update({'is_default': False})
        
        # 创建签名记录
        signature = UserSignature(
            user_id=current_user.id,
            signature_data=signature_data,
            is_default=set_as_default,
            description=description
        )
        
        db.session.add(signature)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'ADD_USER_SIGNATURE', 
            f'添加个人默认签名',
            current_user.id,
            'user'
        )
        
        return jsonify({'success': True, 'message': '个人签名保存成功', 'signature_id': signature.id})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'保存个人签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'保存个人签名失败: {str(e)}'})

# 设置默认签名
@main.route('/user/set_default_signature/<int:signature_id>', methods=['POST'])
@login_required
def set_default_signature(signature_id):
    try:
        # 获取签名
        signature = UserSignature.query.get_or_404(signature_id)
        
        # 检查权限
        if signature.user_id != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限修改此签名'})
        
        # 将所有签名设为非默认
        UserSignature.query.filter_by(user_id=current_user.id).update({'is_default': False})
        
        # 设置选中的签名为默认
        signature.is_default = True
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'SET_DEFAULT_SIGNATURE', 
            f'设置默认个人签名',
            current_user.id,
            'user'
        )
        
        return jsonify({'success': True, 'message': '默认签名设置成功'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'设置默认签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'设置默认签名失败: {str(e)}'})

# 删除用户签名
@main.route('/user/delete_signature/<int:signature_id>', methods=['POST'])
@login_required
def delete_user_signature(signature_id):
    try:
        # 获取签名
        signature = UserSignature.query.get_or_404(signature_id)
        
        # 检查权限
        if signature.user_id != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限删除此签名'})
        
        # 删除签名
        db.session.delete(signature)
        db.session.commit()
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'DELETE_USER_SIGNATURE', 
            f'删除个人签名',
            current_user.id,
            'user'
        )
        
        return jsonify({'success': True, 'message': '签名已删除'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除个人签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'删除个人签名失败: {str(e)}'})

# PDF手写签名页面
@main.route('/file/pdf_signature/<int:file_id>')
@login_required
def pdf_handwriting_signature(file_id):
    """PDF文档手写签名页面"""
    try:
        file = File.query.get_or_404(file_id)

        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限访问此文件', 'error')
                return redirect(url_for('main.index'))

        # 检查文件类型
        if not file.original_filename.lower().endswith('.pdf'):
            flash('此功能仅支持PDF文件', 'error')
            return redirect(url_for('main.file_detail', file_id=file_id))

        # 获取文件已有的签名
        try:
            signatures = Signature.query.filter_by(
                file_id=file.id,
                signature_type='handwriting'
            ).order_by(Signature.signature_date.desc()).all()
        except Exception as e:
            current_app.logger.error(f'查询PDF签名失败: {str(e)}')
            signatures = []

        # 获取用户已保存的签名
        saved_signatures = []
        try:
            # 直接从UserSignature表获取用户的签名数据
            user_signatures = UserSignature.query.filter_by(user_id=current_user.id)\
                .order_by(UserSignature.created_at.desc())\
                .limit(10)\
                .all()

            for user_signature in user_signatures:
                saved_signatures.append({
                    'id': user_signature.id,
                    'date': user_signature.created_at.strftime('%Y-%m-%d %H:%M'),
                    'data': user_signature.signature_data if user_signature.signature_data else ''
                })

        except Exception as e:
            current_app.logger.error(f"获取用户已保存签名时出错: {str(e)}")

        # 记录操作日志
        log_user_action(
            current_user,
            'VIEW_PDF_SIGNATURE',
            f'查看PDF手写签名页面: {file.original_filename}',
            file.id,
            'file'
        )

        return render_template('preview/pdf_sign.html',
                              file=file,
                              signatures=signatures,
                              saved_signatures=saved_signatures)
    except Exception as e:
        current_app.logger.error(f'访问PDF签名页面失败: {str(e)}')
        flash('访问PDF签名页面失败', 'error')
        return redirect(url_for('main.index'))

# 保存PDF手写签名
@main.route('/file/save_pdf_signature/<int:file_id>', methods=['POST'])
@login_required
def save_pdf_signature(file_id):
    """保存PDF手写签名"""
    try:
        file = File.query.get_or_404(file_id)

        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                return jsonify({'success': False, 'message': '您没有权限为此文件添加签名'})

        # 获取签名数据
        signature_data = request.form.get('signature_data')
        position_x = request.form.get('position_x', '0')
        position_y = request.form.get('position_y', '0')
        signature_width = request.form.get('signature_width', '150')
        signature_height = request.form.get('signature_height', '75')
        save_as_template = request.form.get('save_as_template', 'false') == 'true'

        if not signature_data:
            return jsonify({'success': False, 'message': '签名数据不能为空'})

        # 验证签名数据格式
        if not signature_data.startswith('data:image/'):
            return jsonify({'success': False, 'message': '签名数据格式无效'})

        try:
            # 构建位置信息
            position_info = f"{position_x},{position_y},{signature_width},{signature_height}"

            # 构建元数据
            metadata = {
                'position': {
                    'x': int(float(position_x)),
                    'y': int(float(position_y)),
                    'width': int(float(signature_width)),
                    'height': int(float(signature_height))
                },
                'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat(),
                'user_agent': request.headers.get('User-Agent', ''),
                'ip_address': request.remote_addr
            }

            # 创建签名记录
            signature = Signature(
                user_id=current_user.id,
                file_id=file.id,
                signature_data=signature_data,
                signature_type='handwriting',
                signature_position=position_info,
                signature_metadata=json.dumps(metadata)
            )

            db.session.add(signature)

            # 如果用户选择保存为模板，也保存到用户签名表
            if save_as_template:
                # 检查是否已存在相同的签名
                existing_user_signature = UserSignature.query.filter_by(
                    user_id=current_user.id,
                    signature_data=signature_data
                ).first()

                if not existing_user_signature:
                    user_signature = UserSignature(
                        user_id=current_user.id,
                        signature_data=signature_data,
                        description=f'PDF签名 - {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                        is_default=False
                    )
                    db.session.add(user_signature)

            db.session.commit()

            # 记录操作日志
            log_user_action(
                current_user,
                'ADD_PDF_SIGNATURE',
                f'为PDF文件添加手写签名: {file.original_filename}',
                file.id,
                'file'
            )

            return jsonify({
                'success': True,
                'message': '签名保存成功',
                'signature_id': signature.id,
                'template_saved': save_as_template
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'保存PDF签名失败: {str(e)}')
            return jsonify({'success': False, 'message': f'保存签名失败: {str(e)}'})

    except Exception as e:
        current_app.logger.error(f'保存PDF签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'保存签名失败: {str(e)}'})

# 删除PDF签名
@main.route('/file/delete_pdf_signature/<int:signature_id>', methods=['POST'])
@login_required
def delete_pdf_signature(signature_id):
    """删除PDF签名"""
    try:
        signature = Signature.query.get_or_404(signature_id)

        # 检查权限 - 只有管理员或签名创建者可以删除
        if not is_super_admin(current_user) and signature.user_id != current_user.id:
            return jsonify({'success': False, 'message': '您没有权限删除此签名'})

        # 获取文件信息
        file_name = signature.file.original_filename if signature.file else "未知文件"
        file_id = signature.file_id

        # 删除签名
        db.session.delete(signature)
        db.session.commit()

        # 记录操作日志
        log_user_action(
            current_user,
            'DELETE_PDF_SIGNATURE',
            f'删除PDF文件签名: {file_name}',
            file_id,
            'file'
        )

        return jsonify({'success': True, 'message': '签名删除成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除PDF签名失败: {str(e)}')
        return jsonify({'success': False, 'message': f'删除签名失败: {str(e)}'})

# 获取PDF签名列表
@main.route('/file/get_pdf_signatures/<int:file_id>')
@login_required
def get_pdf_signatures(file_id):
    """获取PDF文件的所有签名"""
    try:
        file = File.query.get_or_404(file_id)

        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                return jsonify({'success': False, 'message': '您没有权限查看此文件的签名'})

        # 获取签名列表
        signatures = Signature.query.filter_by(
            file_id=file.id,
            signature_type='handwriting'
        ).order_by(Signature.signature_date.desc()).all()

        signature_list = []
        for sig in signatures:
            # 解析位置信息
            position = {'x': 0, 'y': 0, 'width': 150, 'height': 75}
            if sig.signature_position:
                try:
                    pos_parts = sig.signature_position.split(',')
                    if len(pos_parts) >= 4:
                        position = {
                            'x': int(float(pos_parts[0])),
                            'y': int(float(pos_parts[1])),
                            'width': int(float(pos_parts[2])),
                            'height': int(float(pos_parts[3]))
                        }
                except:
                    pass

            signature_list.append({
                'id': sig.id,
                'user': sig.user.username,
                'date': sig.signature_date.strftime('%Y-%m-%d %H:%M:%S'),
                'data': sig.signature_data,
                'position': position
            })

        return jsonify({
            'success': True,
            'signatures': signature_list
        })

    except Exception as e:
        current_app.logger.error(f'获取PDF签名列表失败: {str(e)}')
        return jsonify({'success': False, 'message': f'获取签名列表失败: {str(e)}'})