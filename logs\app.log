2025-04-10 11:32:56,118 [INFO] File Manager startup
2025-04-10 11:32:59,265 [INFO] File Manager startup
2025-04-10 11:33:18,241 [INFO] File Manager startup
2025-04-10 11:33:21,277 [INFO] 生成验证码: ZG4R
2025-04-10 11:33:21,313 [INFO] 生成验证码: XZA5
2025-04-10 11:33:30,334 [INFO] 验证码比对: 输入=xza5, 存储=xza5
2025-04-10 11:33:34,072 [INFO] 安全问题验证: 用户=cv24051
2025-04-10 11:33:34,195 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-10 11:33:34,213 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:34,213 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:34,298 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:34,303 [INFO] 返回关键字数量: 0
2025-04-10 11:33:34,305 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:34,307 [INFO] 返回关键字数量: 0
2025-04-10 11:33:36,016 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:36,016 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:36,076 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:36,077 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:36,079 [INFO] 返回关键字数量: 0
2025-04-10 11:33:36,080 [INFO] 返回关键字数量: 0
2025-04-10 11:33:36,775 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:36,775 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:36,834 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:36,835 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:36,837 [INFO] 返回关键字数量: 0
2025-04-10 11:33:36,837 [INFO] 返回关键字数量: 0
2025-04-10 11:33:37,454 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:37,455 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:37,513 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:37,515 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:37,516 [INFO] 返回关键字数量: 0
2025-04-10 11:33:37,517 [INFO] 返回关键字数量: 0
2025-04-10 11:33:37,970 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:37,971 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:38,035 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:38,037 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:38,037 [INFO] 返回关键字数量: 0
2025-04-10 11:33:38,039 [INFO] 返回关键字数量: 0
2025-04-10 11:33:38,908 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:38,908 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:38,970 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:38,971 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:38,973 [INFO] 返回关键字数量: 0
2025-04-10 11:33:38,974 [INFO] 返回关键字数量: 0
2025-04-10 11:33:39,348 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:39,348 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:39,411 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:39,412 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:39,413 [INFO] 返回关键字数量: 0
2025-04-10 11:33:39,415 [INFO] 返回关键字数量: 0
2025-04-10 11:33:39,716 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:39,717 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:39,786 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:39,787 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:39,789 [INFO] 返回关键字数量: 0
2025-04-10 11:33:39,789 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,096 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:40,096 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:40,164 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:40,166 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:40,167 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,168 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,396 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:40,397 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:40,450 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:40,451 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:40,452 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,453 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,783 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:40,783 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:40,839 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:40,841 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:40,842 [INFO] 返回关键字数量: 0
2025-04-10 11:33:40,842 [INFO] 返回关键字数量: 0
2025-04-10 11:33:41,141 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:41,141 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:41,205 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:41,206 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:41,208 [INFO] 返回关键字数量: 0
2025-04-10 11:33:41,208 [INFO] 返回关键字数量: 0
2025-04-10 11:33:41,638 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:41,639 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:41,693 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:41,694 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:41,695 [INFO] 返回关键字数量: 0
2025-04-10 11:33:41,696 [INFO] 返回关键字数量: 0
2025-04-10 11:33:42,012 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:42,013 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:42,068 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:42,069 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:42,071 [INFO] 返回关键字数量: 0
2025-04-10 11:33:42,071 [INFO] 返回关键字数量: 0
2025-04-10 11:33:42,525 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:42,525 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:42,585 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:42,586 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:42,588 [INFO] 返回关键字数量: 0
2025-04-10 11:33:42,588 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,005 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:43,005 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:43,061 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:43,064 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:43,064 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,066 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,491 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:43,491 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:43,558 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:43,559 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:43,561 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,561 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,893 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:43,893 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:43,943 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:43,945 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:43,945 [INFO] 返回关键字数量: 0
2025-04-10 11:33:43,947 [INFO] 返回关键字数量: 0
2025-04-10 11:33:44,492 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:44,492 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:44,548 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:44,549 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:44,551 [INFO] 返回关键字数量: 0
2025-04-10 11:33:44,552 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,021 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:45,021 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:45,083 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:45,084 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:45,086 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,087 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,436 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:45,437 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:45,492 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:45,495 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:45,495 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,496 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,868 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:45,868 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:45,928 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:45,929 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:45,931 [INFO] 返回关键字数量: 0
2025-04-10 11:33:45,931 [INFO] 返回关键字数量: 0
2025-04-10 11:33:46,311 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:33:46,311 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:33:46,372 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:33:46,373 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:33:46,375 [INFO] 返回关键字数量: 0
2025-04-10 11:33:46,375 [INFO] 返回关键字数量: 0
2025-04-10 11:34:42,046 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:34:42,046 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:34:42,132 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:34:42,135 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:34:42,136 [INFO] 返回关键字数量: 0
2025-04-10 11:34:42,137 [INFO] 返回关键字数量: 0
2025-04-10 11:34:42,796 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:34:42,797 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:34:42,866 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:34:42,867 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:34:42,868 [INFO] 返回关键字数量: 0
2025-04-10 11:34:42,869 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,045 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:34:43,045 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:34:43,119 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:34:43,120 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:34:43,122 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,123 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,269 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:34:43,270 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:34:43,335 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:34:43,337 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,339 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:34:43,340 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,493 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:34:43,493 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:34:43,558 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:34:43,560 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:34:43,560 [INFO] 返回关键字数量: 0
2025-04-10 11:34:43,562 [INFO] 返回关键字数量: 0
2025-04-10 11:35:58,832 [INFO] File Manager startup
2025-04-10 11:36:03,379 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:03,379 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:03,488 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:03,493 [INFO] 返回关键字数量: 0
2025-04-10 11:36:03,497 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:03,499 [INFO] 返回关键字数量: 0
2025-04-10 11:36:04,990 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:04,991 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:05,054 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:05,057 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:05,057 [INFO] 返回关键字数量: 0
2025-04-10 11:36:05,059 [INFO] 返回关键字数量: 0
2025-04-10 11:36:05,588 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:05,589 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:05,646 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:05,648 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:05,648 [INFO] 返回关键字数量: 0
2025-04-10 11:36:05,650 [INFO] 返回关键字数量: 0
2025-04-10 11:36:06,123 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:06,123 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:06,183 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:06,184 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:06,186 [INFO] 返回关键字数量: 0
2025-04-10 11:36:06,186 [INFO] 返回关键字数量: 0
2025-04-10 11:36:06,746 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:06,746 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:06,804 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:06,805 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:06,807 [INFO] 返回关键字数量: 0
2025-04-10 11:36:06,807 [INFO] 返回关键字数量: 0
2025-04-10 11:36:08,471 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 11:36:08,477 [INFO] 找到 1 条日志记录
2025-04-10 11:36:21,287 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:36:21,287 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:36:21,351 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:36:21,352 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:36:21,354 [INFO] 返回关键字数量: 0
2025-04-10 11:36:21,355 [INFO] 返回关键字数量: 0
2025-04-10 11:37:07,166 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:07,166 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:07,218 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:07,219 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:07,221 [INFO] 返回关键字数量: 0
2025-04-10 11:37:07,221 [INFO] 返回关键字数量: 0
2025-04-10 11:37:13,376 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-10 11:37:13,387 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,403 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,415 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,425 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,432 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,439 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,446 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,451 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,457 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,464 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,469 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-10 11:37:13,508 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:13,508 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:13,568 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:13,568 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:13,570 [INFO] 返回关键字数量: 0
2025-04-10 11:37:13,571 [INFO] 返回关键字数量: 0
2025-04-10 11:37:14,401 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:14,401 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:14,460 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:14,461 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:14,462 [INFO] 返回关键字数量: 0
2025-04-10 11:37:14,463 [INFO] 返回关键字数量: 0
2025-04-10 11:37:17,941 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:17,942 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:17,991 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:17,993 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:17,994 [INFO] 返回关键字数量: 0
2025-04-10 11:37:17,994 [INFO] 返回关键字数量: 0
2025-04-10 11:37:19,545 [INFO] 生成验证码: BVPV
2025-04-10 11:37:19,574 [INFO] 生成验证码: KRV5
2025-04-10 11:37:29,324 [INFO] 验证码比对: 输入=krv5, 存储=krv5
2025-04-10 11:37:29,445 [INFO] 用户 林志勇 登录成功
2025-04-10 11:37:29,452 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:29,452 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:29,508 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:29,510 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:29,513 [INFO] 返回关键字数量: 0
2025-04-10 11:37:29,516 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,256 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:31,257 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:31,333 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:31,335 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:31,336 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,337 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,665 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:31,665 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:31,759 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:31,761 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:31,762 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,764 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,837 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:31,837 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:31,953 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:31,954 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:31,956 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,957 [INFO] 返回关键字数量: 0
2025-04-10 11:37:31,982 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:31,983 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:32,051 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:32,051 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:32,054 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,054 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,127 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:32,127 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:32,193 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:32,196 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:32,196 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,198 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,309 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:32,310 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:32,395 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:32,397 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:32,398 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,399 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,511 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:32,512 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:32,605 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:32,606 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:32,607 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,608 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,736 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:32,737 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:32,822 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:32,825 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:32,825 [INFO] 返回关键字数量: 0
2025-04-10 11:37:32,827 [INFO] 返回关键字数量: 0
2025-04-10 11:37:39,575 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:39,575 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:39,633 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:39,634 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:39,636 [INFO] 返回关键字数量: 0
2025-04-10 11:37:39,637 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,067 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:41,068 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:41,165 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:41,166 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:41,168 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,168 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,344 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:41,344 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:41,429 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:41,432 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:41,433 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,434 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,525 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:41,525 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:41,592 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:41,593 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:41,594 [INFO] 返回关键字数量: 0
2025-04-10 11:37:41,595 [INFO] 返回关键字数量: 0
2025-04-10 11:37:54,795 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:37:54,795 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:37:54,893 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:37:54,894 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:37:54,896 [INFO] 返回关键字数量: 0
2025-04-10 11:37:54,896 [INFO] 返回关键字数量: 0
2025-04-10 11:38:56,114 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:38:56,114 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:38:56,166 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:38:56,168 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:38:56,169 [INFO] 返回关键字数量: 1
2025-04-10 11:38:56,170 [INFO] 返回关键字数量: 1
2025-04-10 11:39:08,048 [INFO] 关键字筛选请求 - 关键字1: 双层, 关键字2: PE, 选择文件夹: 
2025-04-10 11:39:08,048 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 11:39:08,118 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:08,121 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:08,122 [INFO] 返回关键字数量: 1
2025-04-10 11:39:08,124 [INFO] 返回关键字数量: 1
2025-04-10 11:39:10,980 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:10,980 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:11,049 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:11,051 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:11,051 [INFO] 返回关键字数量: 1
2025-04-10 11:39:11,053 [INFO] 返回关键字数量: 1
2025-04-10 11:39:17,352 [INFO] 用户 cv24051 上传文件: YR-0838 单层PA发热线.pdf 到文件夹: 根目录
2025-04-10 11:39:17,375 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:17,375 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:17,430 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:17,432 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:17,432 [INFO] 返回关键字数量: 1
2025-04-10 11:39:17,435 [INFO] 返回关键字数量: 1
2025-04-10 11:39:20,862 [INFO] 开始删除文件: YR-0838 单层PA发热线.pdf
2025-04-10 11:39:20,863 [INFO] 文件标记为已删除: True, 删除时间: 2025-04-10 11:39:20.862963
2025-04-10 11:39:20,868 [INFO] 数据库更新成功
2025-04-10 11:39:20,896 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:20,897 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:20,947 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:20,947 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:20,949 [INFO] 返回关键字数量: 1
2025-04-10 11:39:20,950 [INFO] 返回关键字数量: 1
2025-04-10 11:39:26,076 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:26,076 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:26,140 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:26,142 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:26,142 [INFO] 返回关键字数量: 1
2025-04-10 11:39:26,145 [INFO] 返回关键字数量: 1
2025-04-10 11:39:27,780 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:27,781 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:27,847 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:27,849 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:27,850 [INFO] 返回关键字数量: 1
2025-04-10 11:39:27,851 [INFO] 返回关键字数量: 1
2025-04-10 11:39:34,269 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:34,269 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:34,341 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:34,342 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:34,343 [INFO] 返回关键字数量: 1
2025-04-10 11:39:34,345 [INFO] 返回关键字数量: 1
2025-04-10 11:39:36,530 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:39:36,530 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:39:36,596 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:39:36,598 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:39:36,599 [INFO] 返回关键字数量: 1
2025-04-10 11:39:36,600 [INFO] 返回关键字数量: 1
2025-04-10 11:39:51,002 [INFO] 生成验证码: FGHY
2025-04-10 11:39:51,037 [INFO] 生成验证码: 23FA
2025-04-10 11:40:09,620 [INFO] 验证码比对: 输入=23fa, 存储=23fa
2025-04-10 11:40:12,259 [INFO] 安全问题验证: 用户=cv24051
2025-04-10 11:40:12,379 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-10 11:40:12,397 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:40:12,397 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:40:12,479 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:40:12,481 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:40:12,485 [INFO] 返回关键字数量: 1
2025-04-10 11:40:12,486 [INFO] 返回关键字数量: 1
2025-04-10 11:40:17,155 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 11:40:17,158 [INFO] 找到 25 条日志记录
2025-04-10 11:41:18,897 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:41:18,897 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:41:18,955 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:41:18,956 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:41:18,958 [INFO] 返回关键字数量: 3
2025-04-10 11:41:18,959 [INFO] 返回关键字数量: 1
2025-04-10 11:41:27,581 [INFO] 关键字筛选请求 - 关键字1: undefined, 关键字2: , 选择文件夹: 
2025-04-10 11:41:27,581 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 11:41:27,640 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:41:27,642 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:41:27,642 [INFO] 返回关键字数量: 3
2025-04-10 11:41:27,644 [INFO] 返回关键字数量: 1
2025-04-10 11:41:43,516 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:41:43,517 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:41:43,582 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:41:43,583 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:41:43,585 [INFO] 返回关键字数量: 3
2025-04-10 11:41:43,586 [INFO] 返回关键字数量: 1
2025-04-10 11:46:14,343 [INFO] File Manager startup
2025-04-10 11:46:16,227 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:46:16,227 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:46:16,387 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:46:16,392 [INFO] 返回关键字数量: 2
2025-04-10 11:46:16,394 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:46:16,397 [INFO] 返回关键字数量: 1
2025-04-10 11:46:16,776 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:46:16,777 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:46:16,837 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:46:16,839 [INFO] 返回关键字数量: 1
2025-04-10 11:46:16,840 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:46:16,843 [INFO] 返回关键字数量: 2
2025-04-10 11:46:21,463 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:46:21,463 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:46:21,516 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:46:21,519 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:46:21,520 [INFO] 返回关键字数量: 2
2025-04-10 11:46:21,520 [INFO] 返回关键字数量: 1
2025-04-10 11:46:40,574 [INFO] 关键字筛选请求 - 关键字1: 单层, 关键字2: PE, 选择文件夹: 
2025-04-10 11:46:40,574 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 11:46:40,648 [INFO] 关键字API请求: folder_id=, type=1
2025-04-10 11:46:40,649 [INFO] 关键字API请求: folder_id=, type=2
2025-04-10 11:46:40,651 [INFO] 返回关键字数量: 2
2025-04-10 11:46:40,652 [INFO] 返回关键字数量: 1
2025-04-10 11:50:46,632 [INFO] File Manager startup
2025-04-10 11:57:29,868 [INFO] File Manager startup
2025-04-10 11:57:32,709 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:57:32,709 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:57:46,040 [ERROR] Exception on /admin/keywords [POST]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 2627, in manage_keywords
    flash(t('This keyword 1 already exists for the selected folder'), 'warning')
          ^
NameError: name 't' is not defined
2025-04-10 11:59:35,086 [INFO] File Manager startup
2025-04-10 11:59:38,560 [ERROR] Exception on /admin/keywords [POST]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 2627, in manage_keywords
    flash(t('This keyword 1 already exists for the selected folder'), 'warning')
          ^
NameError: name 't' is not defined
2025-04-10 11:59:40,138 [ERROR] Exception on /admin/keywords [POST]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 2627, in manage_keywords
    flash(t('This keyword 1 already exists for the selected folder'), 'warning')
          ^
NameError: name 't' is not defined
2025-04-10 11:59:43,749 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 11:59:43,749 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 11:59:53,870 [ERROR] Exception on /admin/keywords [POST]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 2627, in manage_keywords
    flash(t('This keyword 1 already exists for the selected folder'), 'warning')
          ^
NameError: name 't' is not defined
2025-04-10 12:01:38,700 [INFO] File Manager startup
2025-04-10 12:02:23,810 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 12:02:23,810 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 12:02:31,623 [INFO] 关键字筛选请求 - 关键字1: 三层, 关键字2: , 选择文件夹: 
2025-04-10 12:02:31,623 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 12:02:38,518 [INFO] 关键字筛选请求 - 关键字1: 三层, 关键字2: PA, 选择文件夹: 
2025-04-10 12:02:38,518 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 12:02:41,140 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 12:02:41,140 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:33:29,987 [INFO] File Manager startup
2025-04-10 13:33:33,156 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:33:33,156 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:06,169 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:06,170 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:14,936 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:14,936 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:26,279 [INFO] 关键字筛选请求 - 关键字1: {{ request.args.get('keyword1', '') }}, 关键字2: {{ request.args.get('keyword2', '') }}, 选择文件夹: 
2025-04-10 13:34:26,280 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-10 13:34:32,154 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:32,154 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:54,998 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:54,998 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:55,577 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:55,577 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:34:56,968 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:34:56,968 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:09,493 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:09,493 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:11,373 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:11,373 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:13,968 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:13,969 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:26,751 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263326.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 26, 749137, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 2, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:35:26,753 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263326.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 26, 749137, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 2, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:35:26,753 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-10 13:35:26,764 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:26,764 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:29,697 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:29,697 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:30,368 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:30,368 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:32,744 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:32,744 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:38,834 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263338.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 38, 832358, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 3, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:35:38,835 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263338.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 38, 832358, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 3, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:35:38,835 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-10 13:35:38,845 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:38,845 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:51,872 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:51,872 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:35:58,942 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263358.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 58, 940498, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 4, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:35:58,943 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263358.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 35, 58, 940498, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 4, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:35:58,944 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-10 13:35:58,954 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:35:58,955 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:07,174 [INFO] File Manager startup
2025-04-10 13:37:11,681 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:11,681 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:13,173 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:13,173 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:14,259 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:14,259 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:16,406 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:16,406 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:18,743 [INFO] 开始删除文件夹: Specification
2025-04-10 13:37:18,743 [INFO] 处理文件夹: Specification
2025-04-10 13:37:18,743 [INFO] 文件夹标记为已删除: True
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-10 13:37:18,750 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-10 13:37:18,759 [INFO] 数据库更新成功
2025-04-10 13:37:18,795 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:18,795 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:25,135 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:25,135 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:34,180 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263454.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 37, 34, 179173, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 5, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:37:34,182 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263454.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 37, 34, 179173, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 5, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:37:34,182 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-10 13:37:34,193 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:34,194 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:51,679 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263471.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 37, 51, 677260, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 6, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:37:51,681 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744263471.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 10, 13, 37, 51, 677260, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 6, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:37:51,681 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-10 13:37:51,697 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:51,698 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:37:59,792 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:37:59,792 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:38:43,150 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-10 13:38:43,159 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,168 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,174 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,180 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,187 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,195 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,203 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,209 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,216 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,222 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,228 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-10 13:38:43,243 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:38:43,243 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:38:46,057 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:38:46,057 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:38:47,191 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:38:47,192 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:39:01,017 [INFO] 用户 cv24051 上传文件: 工作安排/工作安排.doc 到文件夹: 工作安排
2025-04-10 13:39:01,023 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744263541.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 10, 13, 39, 1, 23571, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 8, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-10 13:39:01,024 [ERROR] 记录用户操作失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744263541.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 10, 13, 39, 1, 23571, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 8, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-10 13:39:01,033 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:39:01,033 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:39:27,455 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP 到文件夹: 123
2025-04-10 13:39:27,471 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP 到文件夹: 123
2025-04-10 13:39:27,493 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP 到文件夹: 123
2025-04-10 13:39:27,513 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP 到文件夹: 123
2025-04-10 13:39:27,530 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP 到文件夹: 123
2025-04-10 13:39:27,544 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP 到文件夹: 123
2025-04-10 13:39:27,558 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 123
2025-04-10 13:39:27,584 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:39:27,584 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:39:29,203 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:39:29,203 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:39:30,402 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:39:30,402 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:39:34,203 [INFO] 开始删除文件夹: 123
2025-04-10 13:39:34,203 [INFO] 处理文件夹: 123
2025-04-10 13:39:34,203 [INFO] 文件夹标记为已删除: True
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-10 13:39:34,207 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-10 13:39:34,214 [INFO] 数据库更新成功
2025-04-10 13:39:34,243 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:39:34,243 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:41:47,444 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:41:47,444 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:41:50,802 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:41:50,802 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:17,816 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:17,816 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:18,530 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:18,530 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:38,228 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:38,229 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:47,350 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:47,350 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:48,334 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:48,334 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:48,772 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:48,773 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:42:49,171 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:42:49,172 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:00,508 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:00,509 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:08,245 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:08,246 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:16,631 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:16,632 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:18,267 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:18,267 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:21,874 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:21,875 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:22,465 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:22,465 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:26,273 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:26,273 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:43:28,042 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:43:28,042 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:44:40,982 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:44:40,982 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:48:18,275 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:48:18,275 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:48:31,155 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:48:31,156 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:51:16,967 [INFO] 生成验证码: XGU3
2025-04-10 13:51:17,010 [INFO] 生成验证码: YA8B
2025-04-10 13:51:38,869 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:51:38,869 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:51:39,586 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:51:39,587 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:51:40,707 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:51:40,707 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:51:52,595 [INFO] 开始删除文件夹: 工作安排
2025-04-10 13:51:52,595 [INFO] 处理文件夹: 工作安排
2025-04-10 13:51:52,596 [INFO] 文件夹标记为已删除: True
2025-04-10 13:51:52,599 [INFO] 文件标记为已删除: 工作安排/工作安排.doc
2025-04-10 13:51:52,604 [INFO] 数据库更新成功
2025-04-10 13:51:52,634 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:51:52,634 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:00,562 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:00,562 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:08,913 [INFO] 用户 cv24051 上传文件: 222/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf 到文件夹: 222
2025-04-10 13:52:08,921 [INFO] 用户 cv24051 上传文件: 222/项目管理系统 - 副本.doc 到文件夹: 222
2025-04-10 13:52:08,927 [INFO] 用户 cv24051 上传文件: 222/项目管理系统.doc 到文件夹: 222
2025-04-10 13:52:08,946 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:08,947 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:10,050 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:10,051 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:12,302 [INFO] 验证码比对: 输入=ya8b, 存储=ya8b
2025-04-10 13:52:12,350 [INFO] 生成验证码: LL2L
2025-04-10 13:52:12,362 [INFO] 生成验证码: Y9VJ
2025-04-10 13:52:16,878 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:16,878 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:22,046 [INFO] 开始删除文件夹: 222
2025-04-10 13:52:22,047 [INFO] 处理文件夹: 222
2025-04-10 13:52:22,047 [INFO] 文件夹标记为已删除: True
2025-04-10 13:52:22,050 [INFO] 文件标记为已删除: 222/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf
2025-04-10 13:52:22,050 [INFO] 文件标记为已删除: 222/项目管理系统 - 副本.doc
2025-04-10 13:52:22,050 [INFO] 文件标记为已删除: 222/项目管理系统.doc
2025-04-10 13:52:22,057 [INFO] 数据库更新成功
2025-04-10 13:52:22,086 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:22,086 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:22,534 [INFO] 验证码比对: 输入=y9vj, 存储=y9vj
2025-04-10 13:52:22,581 [INFO] 生成验证码: R63W
2025-04-10 13:52:22,590 [INFO] 生成验证码: FYK9
2025-04-10 13:52:30,516 [INFO] 恢复文件夹: 222
2025-04-10 13:52:30,519 [INFO] 恢复文件: 222/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf
2025-04-10 13:52:30,519 [INFO] 恢复文件: 222/项目管理系统 - 副本.doc
2025-04-10 13:52:30,519 [INFO] 恢复文件: 222/项目管理系统.doc
2025-04-10 13:52:30,525 [INFO] 恢复成功
2025-04-10 13:52:32,074 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:32,074 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:33,106 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:33,107 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:34,275 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:34,275 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:52:38,039 [INFO] 开始删除文件夹: 222
2025-04-10 13:52:38,039 [INFO] 处理文件夹: 222
2025-04-10 13:52:38,039 [INFO] 文件夹标记为已删除: True
2025-04-10 13:52:38,041 [INFO] 文件标记为已删除: 222/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf
2025-04-10 13:52:38,041 [INFO] 文件标记为已删除: 222/项目管理系统 - 副本.doc
2025-04-10 13:52:38,041 [INFO] 文件标记为已删除: 222/项目管理系统.doc
2025-04-10 13:52:38,047 [INFO] 数据库更新成功
2025-04-10 13:52:38,076 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:52:38,077 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:40,198 [INFO] File Manager startup
2025-04-10 13:55:40,307 [INFO] File Manager startup
2025-04-10 13:55:40,355 [INFO] File Manager startup
2025-04-10 13:55:40,416 [INFO] File Manager startup
2025-04-10 13:55:40,470 [INFO] File Manager startup
2025-04-10 13:55:40,509 [INFO] File Manager startup
2025-04-10 13:55:40,572 [INFO] File Manager startup
2025-04-10 13:55:40,572 [INFO] File Manager startup
2025-04-10 13:55:49,489 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:49,489 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:50,069 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:50,070 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:50,303 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:50,303 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:50,577 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:50,577 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:50,733 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:50,733 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:50,938 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:50,938 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:52,085 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:55:52,086 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:55:57,095 [INFO] 用户 cv24051 退出登录
2025-04-10 13:55:57,142 [INFO] 生成验证码: 68B2
2025-04-10 13:55:57,161 [INFO] 生成验证码: 5SHR
2025-04-10 13:56:13,942 [INFO] 验证码比对: 输入=5shr, 存储=5shr
2025-04-10 13:56:16,677 [INFO] 安全问题验证: 用户=cv24051
2025-04-10 13:56:16,793 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-10 13:56:16,808 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:56:16,808 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:56:19,109 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:56:19,110 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:56:22,646 [INFO] File Manager startup
2025-04-10 13:56:34,520 [INFO] 生成验证码: 6BLP
2025-04-10 13:56:34,552 [INFO] 生成验证码: WBR3
2025-04-10 13:56:45,534 [ERROR] Exception on /get_folder_permissions/7 [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3322, in get_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 13:56:46,854 [ERROR] Exception on /get_folder_permissions/7 [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3322, in get_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 13:56:47,629 [ERROR] Exception on /get_folder_permissions/7 [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3322, in get_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 13:57:09,382 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:57:09,382 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:57:11,020 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 13:57:11,020 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 13:57:12,739 [ERROR] Exception on /get_folder_permissions/7 [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3322, in get_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 13:57:35,987 [ERROR] Exception on /get_folder_permissions/7 [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3322, in get_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 13:58:42,049 [INFO] File Manager startup
2025-04-10 13:58:42,082 [INFO] File Manager startup
2025-04-10 13:59:42,360 [INFO] File Manager startup
2025-04-10 13:59:42,432 [INFO] File Manager startup
2025-04-10 14:00:41,658 [INFO] File Manager startup
2025-04-10 14:00:41,692 [INFO] File Manager startup
2025-04-10 14:01:41,911 [INFO] File Manager startup
2025-04-10 14:01:41,985 [INFO] File Manager startup
2025-04-10 14:07:44,147 [INFO] File Manager startup
2025-04-10 14:07:47,252 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:47,252 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:47,850 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:47,850 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:48,324 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:48,324 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:48,499 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:48,499 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:48,682 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:48,682 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:48,874 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:48,874 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:07:49,033 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:07:49,033 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:09:13,287 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:09:13,288 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:09:21,873 [ERROR] Exception on /update_folder_permissions [POST]
Traceback (most recent call last):
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/myenv1/lib/python3.12/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/fil_man_english/app/routes.py", line 3370, in update_folder_permissions_api
    return jsonify({'success': False, 'message': t('You do not have permission to manage this folder')})
                                                 ^
NameError: name 't' is not defined
2025-04-10 14:09:21,887 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:09:21,887 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:09:26,590 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:09:26,590 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:09:27,753 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:09:27,753 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:09:31,064 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:09:31,065 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:10:44,712 [INFO] 用户 林志勇 退出登录
2025-04-10 14:10:44,768 [INFO] 生成验证码: 85EF
2025-04-10 14:10:44,783 [INFO] 生成验证码: JL5G
2025-04-10 14:10:59,487 [INFO] 验证码比对: 输入=jl5g, 存储=jl5g
2025-04-10 14:10:59,612 [INFO] 用户 Ron 登录成功
2025-04-10 14:10:59,620 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:10:59,621 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:12:39,856 [INFO] 生成验证码: UMWX
2025-04-10 14:12:39,884 [INFO] 生成验证码: YHC3
2025-04-10 14:12:41,489 [INFO] 生成验证码: 5LJP
2025-04-10 14:12:41,516 [INFO] 生成验证码: SPRH
2025-04-10 14:14:00,233 [INFO] File Manager startup
2025-04-10 14:14:00,308 [INFO] File Manager startup
2025-04-10 14:14:00,416 [INFO] File Manager startup
2025-04-10 14:14:00,518 [INFO] File Manager startup
2025-04-10 14:14:00,585 [INFO] File Manager startup
2025-04-10 14:14:00,591 [INFO] File Manager startup
2025-04-10 14:14:00,654 [INFO] File Manager startup
2025-04-10 14:14:00,729 [INFO] File Manager startup
2025-04-10 14:15:24,924 [INFO] File Manager startup
2025-04-10 14:15:25,009 [INFO] File Manager startup
2025-04-10 14:16:25,295 [INFO] File Manager startup
2025-04-10 14:16:25,372 [INFO] File Manager startup
2025-04-10 14:17:57,765 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:17:57,765 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:18:03,120 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 14:18:03,120 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 14:18:31,880 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 14:18:31,884 [INFO] 找到 95 条日志记录
2025-04-10 14:18:36,224 [INFO] 请求日志页面：页码=2, 操作=, 用户=None, 搜索=
2025-04-10 14:18:36,225 [INFO] 找到 95 条日志记录
2025-04-10 14:18:38,801 [INFO] 请求日志页面：页码=3, 操作=, 用户=None, 搜索=
2025-04-10 14:18:38,803 [INFO] 找到 95 条日志记录
2025-04-10 14:18:40,375 [INFO] 请求日志页面：页码=4, 操作=, 用户=None, 搜索=
2025-04-10 14:18:40,378 [INFO] 找到 95 条日志记录
2025-04-10 14:18:41,978 [INFO] 请求日志页面：页码=5, 操作=, 用户=None, 搜索=
2025-04-10 14:18:41,980 [INFO] 找到 95 条日志记录
2025-04-10 14:18:46,410 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 14:18:46,419 [INFO] 找到 95 条日志记录
2025-04-10 15:20:59,335 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:20:59,335 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:21:00,892 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:21:00,893 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:21:32,306 [INFO] File Manager startup
2025-04-10 15:21:32,310 [INFO] File Manager startup
2025-04-10 15:40:17,661 [INFO] 生成验证码: 3YTD
2025-04-10 15:40:17,699 [INFO] 生成验证码: QYW5
2025-04-10 15:40:27,927 [INFO] 验证码比对: 输入=qyw5, 存储=qyw5
2025-04-10 15:40:27,976 [INFO] 生成验证码: H3UX
2025-04-10 15:40:27,997 [INFO] 生成验证码: NVVP
2025-04-10 15:40:38,379 [INFO] 验证码比对: 输入=nvvp, 存储=nvvp
2025-04-10 15:40:38,411 [INFO] 生成验证码: YHMX
2025-04-10 15:40:38,422 [INFO] 生成验证码: YW6B
2025-04-10 15:45:50,121 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:45:50,121 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:45:51,804 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:45:51,805 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:46:23,266 [INFO] File Manager startup
2025-04-10 15:51:16,181 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:51:16,182 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:51:17,307 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 15:51:17,308 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 15:51:22,731 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 15:51:22,739 [INFO] 找到 98 条日志记录
2025-04-10 17:12:34,613 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:12:34,615 [INFO] 找到 98 条日志记录
2025-04-10 17:12:38,119 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 17:12:38,120 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 17:17:52,894 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 17:17:52,894 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 17:17:57,664 [INFO] 用户 cv24051 退出登录
2025-04-10 17:17:57,701 [INFO] 生成验证码: AYT2
2025-04-10 17:17:57,728 [INFO] 生成验证码: TM94
2025-04-10 17:18:12,333 [INFO] 生成验证码: 5GFR
2025-04-10 17:18:12,357 [INFO] 生成验证码: 4VHD
2025-04-10 17:18:25,963 [INFO] 验证码比对: 输入=4vhd, 存储=4vhd
2025-04-10 17:18:28,431 [INFO] 安全问题验证: 用户=cv24051
2025-04-10 17:18:28,549 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-10 17:18:28,562 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-10 17:18:28,563 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-10 17:19:51,981 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:51,985 [INFO] 找到 100 条日志记录
2025-04-10 17:19:53,868 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:53,870 [INFO] 找到 100 条日志记录
2025-04-10 17:19:55,797 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:55,799 [INFO] 找到 100 条日志记录
2025-04-10 17:19:56,461 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:56,463 [INFO] 找到 100 条日志记录
2025-04-10 17:19:56,885 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:56,887 [INFO] 找到 100 条日志记录
2025-04-10 17:19:58,387 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:19:58,389 [INFO] 找到 100 条日志记录
2025-04-10 17:20:00,558 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:20:00,560 [INFO] 找到 100 条日志记录
2025-04-10 17:20:07,321 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-10 17:20:07,327 [INFO] 找到 0 条日志记录
2025-04-11 08:19:38,410 [INFO] 生成验证码: KRZN
2025-04-11 08:19:38,445 [INFO] 生成验证码: EWXN
2025-04-11 08:19:49,609 [INFO] 验证码比对: 输入=ewxn, 存储=ewxn
2025-04-11 08:19:49,646 [INFO] 生成验证码: QLY4
2025-04-11 08:19:49,663 [INFO] 生成验证码: NZ2E
2025-04-11 08:20:04,156 [INFO] 验证码比对: 输入=nz2e, 存储=nz2e
2025-04-11 08:20:04,185 [INFO] 生成验证码: 5TGX
2025-04-11 08:20:04,203 [INFO] 生成验证码: 6EYE
2025-04-11 08:20:28,775 [INFO] 验证码比对: 输入=6eye, 存储=6eye
2025-04-11 08:20:28,810 [INFO] 生成验证码: 5MQL
2025-04-11 08:20:28,826 [INFO] 生成验证码: AZJZ
2025-04-11 08:30:22,740 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:30:22,741 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:30:27,013 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:27,018 [INFO] 找到 100 条日志记录
2025-04-11 08:30:33,281 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:33,285 [INFO] 找到 100 条日志记录
2025-04-11 08:30:34,029 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:34,031 [INFO] 找到 100 条日志记录
2025-04-11 08:30:34,287 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:34,290 [INFO] 找到 100 条日志记录
2025-04-11 08:30:34,507 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:34,510 [INFO] 找到 100 条日志记录
2025-04-11 08:30:34,715 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:34,717 [INFO] 找到 100 条日志记录
2025-04-11 08:30:35,276 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:35,279 [INFO] 找到 100 条日志记录
2025-04-11 08:30:49,808 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 08:30:49,810 [INFO] 找到 100 条日志记录
2025-04-11 08:31:16,473 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:31:16,473 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:38:51,536 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:38:51,536 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:38:53,361 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:38:53,361 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:38:58,460 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:38:58,460 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:39:00,049 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:39:00,049 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:39:03,138 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 08:39:03,138 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 08:39:33,594 [INFO] File Manager startup
2025-04-11 08:39:33,661 [INFO] File Manager startup
2025-04-11 08:40:33,896 [INFO] File Manager startup
2025-04-11 08:40:33,905 [INFO] File Manager startup
2025-04-11 08:41:34,099 [INFO] File Manager startup
2025-04-11 08:41:34,166 [INFO] File Manager startup
2025-04-11 08:42:34,435 [INFO] File Manager startup
2025-04-11 08:42:34,517 [INFO] File Manager startup
2025-04-11 08:43:34,772 [INFO] File Manager startup
2025-04-11 08:43:34,778 [INFO] File Manager startup
2025-04-11 09:07:53,374 [INFO] 生成验证码: SY2D
2025-04-11 09:07:53,415 [INFO] 生成验证码: Q2DE
2025-04-11 10:27:45,831 [INFO] 生成验证码: J4HJ
2025-04-11 10:27:45,865 [INFO] 生成验证码: 2TLE
2025-04-11 11:11:43,229 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 11:11:43,229 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 11:12:13,917 [INFO] File Manager startup
2025-04-11 11:12:13,925 [INFO] File Manager startup
2025-04-11 11:13:14,212 [INFO] File Manager startup
2025-04-11 11:13:14,239 [INFO] File Manager startup
2025-04-11 11:14:14,500 [INFO] File Manager startup
2025-04-11 11:14:14,594 [INFO] File Manager startup
2025-04-11 11:15:14,847 [INFO] File Manager startup
2025-04-11 11:15:14,922 [INFO] File Manager startup
2025-04-11 11:16:14,203 [INFO] File Manager startup
2025-04-11 11:16:14,208 [INFO] File Manager startup
2025-04-11 13:36:23,058 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 13:36:23,058 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 13:36:29,866 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 13:36:29,866 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 13:38:01,815 [INFO] File Manager startup
2025-04-11 13:38:01,881 [INFO] File Manager startup
2025-04-11 13:39:02,172 [INFO] File Manager startup
2025-04-11 13:39:02,237 [INFO] File Manager startup
2025-04-11 13:40:02,495 [INFO] File Manager startup
2025-04-11 13:40:02,540 [INFO] File Manager startup
2025-04-11 13:41:02,749 [INFO] File Manager startup
2025-04-11 13:41:02,773 [INFO] File Manager startup
2025-04-11 13:48:11,336 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 13:48:11,337 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 13:48:41,724 [INFO] 生成验证码: SE7N
2025-04-11 13:48:41,748 [INFO] 生成验证码: GAF6
2025-04-11 13:49:44,569 [INFO] File Manager startup
2025-04-11 13:49:44,641 [INFO] File Manager startup
2025-04-11 13:49:48,931 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 13:49:48,931 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 13:50:44,863 [INFO] File Manager startup
2025-04-11 13:50:44,884 [INFO] File Manager startup
2025-04-11 13:51:45,150 [INFO] File Manager startup
2025-04-11 13:51:45,163 [INFO] File Manager startup
2025-04-11 13:52:44,419 [INFO] File Manager startup
2025-04-11 13:52:44,426 [INFO] File Manager startup
2025-04-11 14:39:19,629 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:39:19,629 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:39:51,029 [INFO] File Manager startup
2025-04-11 14:39:51,087 [INFO] File Manager startup
2025-04-11 14:40:34,821 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353634.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 40, 34, 820007, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 11, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-11 14:40:34,823 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353634.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 40, 34, 820007, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 11, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-11 14:40:34,823 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-11 14:40:34,849 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:40:34,849 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:40:44,838 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353644.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 40, 44, 836682, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 12, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-11 14:40:44,840 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353644.pptx', 'original_filename': '自动化机/20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 40, 44, 836682, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 12, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-11 14:40:44,840 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-11 14:40:44,850 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:40:44,850 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:40:50,323 [INFO] File Manager startup
2025-04-11 14:40:50,382 [INFO] File Manager startup
2025-04-11 14:40:56,787 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:40:56,787 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:08,544 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:08,544 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:09,919 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:09,919 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:11,258 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:11,259 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:13,510 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:13,510 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:14,790 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:14,790 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:15,572 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:15,573 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:21,968 [INFO] 用户 cv24051 上传文件: 980 法国头铆压+尾部处理+头尾注塑成型+成品电测生产线(1).pdf 到文件夹: 自动化机
2025-04-11 14:41:21,980 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353681.pptx', 'original_filename': '20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 41, 21, 979948, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 13, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-11 14:41:21,982 [ERROR] 上传失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '20250120_1744353681.pptx', 'original_filename': '20250120（自动机方案）.pptx', 'file_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'file_size': 14617405, 'upload_date': datetime.datetime(2025, 4, 11, 14, 41, 21, 979948, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 13, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-11 14:41:21,992 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:21,992 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:30,912 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:30,912 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:48,894 [INFO] 用户 cv24051 上传文件: ok1.png 到文件夹: 自动化机
2025-04-11 14:41:48,902 [INFO] 用户 cv24051 上传文件: ok2.png 到文件夹: 自动化机
2025-04-11 14:41:48,925 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:41:48,925 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:41:50,626 [INFO] File Manager startup
2025-04-11 14:41:50,669 [INFO] File Manager startup
2025-04-11 14:42:18,194 [INFO] 用户 cv24051 上传文件: YR-0601 三层TPEE发热线.pdf 到文件夹: 自动化机
2025-04-11 14:42:18,205 [INFO] 用户 cv24051 上传文件: YR-0655 三层PE发热线.pdf 到文件夹: 自动化机
2025-04-11 14:42:18,219 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:42:18,219 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:42:19,823 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:42:19,824 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:42:20,865 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:42:20,865 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:42:25,107 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:42:25,107 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:42:26,641 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:42:26,642 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:42:50,935 [INFO] File Manager startup
2025-04-11 14:42:50,986 [INFO] File Manager startup
2025-04-11 14:43:14,701 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:43:14,701 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:43:19,845 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:43:19,846 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: 111, 范围: all
2025-04-11 14:43:45,257 [INFO] File Manager startup
2025-04-11 14:43:45,261 [INFO] File Manager startup
2025-04-11 14:44:45,517 [INFO] File Manager startup
2025-04-11 14:44:45,615 [INFO] File Manager startup
2025-04-11 14:45:36,477 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:45:36,477 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:45:45,868 [INFO] File Manager startup
2025-04-11 14:45:45,917 [INFO] File Manager startup
2025-04-11 14:45:59,241 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:45:59,241 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:46:01,867 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:46:01,868 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:46:07,552 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:46:07,552 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:46:10,379 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:46:10,379 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:46:17,861 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:46:17,861 [INFO] 搜索请求 - 文件名: 处理, 匹配方式: fuzzy, 标签: , 范围: all
2025-04-11 14:46:46,182 [INFO] File Manager startup
2025-04-11 14:46:46,232 [INFO] File Manager startup
2025-04-11 14:47:45,449 [INFO] File Manager startup
2025-04-11 14:47:45,507 [INFO] File Manager startup
2025-04-11 14:53:33,674 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:53:33,674 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:54:05,122 [INFO] File Manager startup
2025-04-11 14:54:05,166 [INFO] File Manager startup
2025-04-11 14:55:04,422 [INFO] File Manager startup
2025-04-11 14:55:04,498 [INFO] File Manager startup
2025-04-11 14:56:04,725 [INFO] File Manager startup
2025-04-11 14:56:04,779 [INFO] File Manager startup
2025-04-11 14:57:05,068 [INFO] File Manager startup
2025-04-11 14:57:05,073 [INFO] File Manager startup
2025-04-11 14:57:54,424 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 14:57:54,425 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 14:58:05,353 [INFO] File Manager startup
2025-04-11 14:58:05,399 [INFO] File Manager startup
2025-04-11 15:10:08,722 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-11 15:10:08,729 [INFO] 找到 112 条日志记录
2025-04-11 15:25:16,081 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 15:25:16,081 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 15:25:19,586 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 15:25:19,586 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 15:25:21,474 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 15:25:21,475 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 15:25:23,741 [INFO] 开始删除文件夹: 自动化机
2025-04-11 15:25:23,741 [INFO] 处理文件夹: 自动化机
2025-04-11 15:25:23,741 [INFO] 文件夹标记为已删除: True
2025-04-11 15:25:23,748 [INFO] 文件标记为已删除: 980 法国头铆压+尾部处理+头尾注塑成型+成品电测生产线(1).pdf
2025-04-11 15:25:23,748 [INFO] 文件标记为已删除: ok1.png
2025-04-11 15:25:23,748 [INFO] 文件标记为已删除: ok2.png
2025-04-11 15:25:23,748 [INFO] 文件标记为已删除: YR-0601 三层TPEE发热线.pdf
2025-04-11 15:25:23,748 [INFO] 文件标记为已删除: YR-0655 三层PE发热线.pdf
2025-04-11 15:25:23,755 [INFO] 数据库更新成功
2025-04-11 15:25:23,772 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 15:25:23,772 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-11 15:50:07,545 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-11 15:50:07,545 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:05:40,578 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:05:40,578 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:05:45,146 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 08:05:45,150 [INFO] 找到 113 条日志记录
2025-04-14 08:05:54,108 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:05:54,108 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:18:07,056 [INFO] 生成验证码: FWBV
2025-04-14 08:18:07,149 [INFO] 生成验证码: 5QFE
2025-04-14 08:18:48,196 [INFO] 验证码比对: 输入=5qfe, 存储=5qfe
2025-04-14 08:18:48,384 [INFO] 生成验证码: 5TP7
2025-04-14 08:18:48,389 [INFO] 生成验证码: TKQV
2025-04-14 08:19:09,585 [INFO] 验证码比对: 输入=tkqv, 存储=tkqv
2025-04-14 08:19:17,653 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 08:19:17,775 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 08:19:17,823 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:19:17,824 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:20:32,150 [INFO] 超级管理员 cv24051 授予 夏欢钰 的高级用户权限
2025-04-14 08:21:32,564 [INFO] 生成验证码: 3DSE
2025-04-14 08:21:32,666 [INFO] 生成验证码: CVF3
2025-04-14 08:22:03,740 [INFO] File Manager startup
2025-04-14 08:22:11,727 [INFO] 验证码比对: 输入=cvf3, 存储=cvf3
2025-04-14 08:22:11,859 [INFO] 用户 魏林 登录成功
2025-04-14 08:22:11,876 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:22:11,876 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:22:36,942 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:22:36,942 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:23:00,670 [INFO] 用户 cv24051 退出登录
2025-04-14 08:23:00,744 [INFO] 生成验证码: QB3P
2025-04-14 08:23:00,836 [INFO] 生成验证码: HTNQ
2025-04-14 08:23:20,288 [INFO] 验证码比对: 输入=htnq, 存储=htnq
2025-04-14 08:23:20,409 [INFO] 用户 夏欢钰 登录成功
2025-04-14 08:23:20,446 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:23:20,446 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:24:08,058 [INFO] File Manager startup
2025-04-14 08:24:08,156 [INFO] File Manager startup
2025-04-14 08:25:08,437 [INFO] File Manager startup
2025-04-14 08:25:08,468 [INFO] File Manager startup
2025-04-14 08:26:07,687 [INFO] File Manager startup
2025-04-14 08:26:07,760 [INFO] File Manager startup
2025-04-14 08:27:07,948 [INFO] File Manager startup
2025-04-14 08:27:08,004 [INFO] File Manager startup
2025-04-14 08:37:16,081 [INFO] 生成验证码: 9Q4E
2025-04-14 08:37:16,086 [INFO] 生成验证码: 496F
2025-04-14 08:37:16,099 [INFO] 生成验证码: 8YPJ
2025-04-14 08:37:16,294 [INFO] 生成验证码: NMJW
2025-04-14 08:37:47,900 [INFO] File Manager startup
2025-04-14 08:38:19,241 [INFO] 生成验证码: RD4R
2025-04-14 08:38:19,284 [INFO] 生成验证码: LM8P
2025-04-14 08:38:33,797 [INFO] 验证码比对: 输入=lm8p, 存储=lm8p
2025-04-14 08:38:33,835 [INFO] 生成验证码: SU2M
2025-04-14 08:38:33,852 [INFO] 生成验证码: CFMJ
2025-04-14 08:40:34,135 [INFO] 验证码比对: 输入=cfmj, 存储=cfmj
2025-04-14 08:40:34,173 [INFO] 生成验证码: 9S4D
2025-04-14 08:40:34,190 [INFO] 生成验证码: 2TLQ
2025-04-14 08:41:43,402 [INFO] 验证码比对: 输入=2tlq, 存储=2tlq
2025-04-14 08:41:47,045 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 08:41:47,162 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 08:41:47,187 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:41:47,187 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:41:49,991 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:41:49,992 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:41:51,576 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:41:51,576 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:41:56,920 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:41:56,921 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:41:57,783 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:41:57,784 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:42:49,162 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:42:49,162 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:42:51,985 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:42:51,986 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:08,675 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:08,676 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:09,699 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:09,699 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:11,484 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:11,484 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:12,396 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:12,396 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:20,643 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:20,643 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:23,768 [INFO] 开始删除文件夹: Specification
2025-04-14 08:43:23,768 [INFO] 处理文件夹: Specification
2025-04-14 08:43:23,768 [INFO] 文件夹标记为已删除: True
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 08:43:23,771 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 08:43:23,777 [INFO] 数据库更新成功
2025-04-14 08:43:23,789 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:23,789 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:30,680 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-14 08:43:30,688 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,695 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,701 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,707 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,714 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,722 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,728 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,735 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,741 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,748 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,754 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-14 08:43:30,775 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:30,775 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:31,530 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:31,530 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:46,658 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:46,658 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:49,236 [INFO] 开始删除文件夹: Specification
2025-04-14 08:43:49,236 [INFO] 处理文件夹: Specification
2025-04-14 08:43:49,236 [INFO] 文件夹标记为已删除: True
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 08:43:49,240 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 08:43:49,241 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 08:43:49,241 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 08:43:49,241 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 08:43:49,251 [INFO] 数据库更新成功
2025-04-14 08:43:49,266 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:49,266 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:43:55,460 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:43:55,460 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:44:21,792 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-14 08:44:21,799 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,805 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,812 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,817 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,824 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,830 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,836 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,843 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,850 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,857 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,864 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-14 08:44:21,879 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:44:21,880 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:44:22,725 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:44:22,725 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:45:07,927 [INFO] 生成验证码: Z2LD
2025-04-14 08:45:07,950 [INFO] 生成验证码: SLK5
2025-04-14 08:45:14,478 [INFO] 验证码比对: 输入=slk5, 存储=slk5
2025-04-14 08:45:17,014 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 08:45:17,137 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 08:45:17,147 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:45:17,147 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:45:18,376 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:45:18,376 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:45:57,629 [INFO] File Manager startup
2025-04-14 08:47:01,470 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:47:01,470 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:47:02,725 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:47:02,725 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:47:18,861 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:47:18,861 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:48:03,547 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:48:03,547 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:48:06,651 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:48:06,651 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:48:16,394 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:48:16,394 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:48:17,746 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:48:17,746 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:48:49,011 [INFO] File Manager startup
2025-04-14 08:55:15,989 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 08:55:15,990 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 08:55:20,271 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 08:55:20,278 [INFO] 找到 188 条日志记录
2025-04-14 08:56:12,682 [INFO] File Manager startup
2025-04-14 09:22:06,960 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 09:22:06,961 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 09:23:38,607 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 09:23:38,611 [INFO] 找到 189 条日志记录
2025-04-14 09:31:29,762 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 09:31:29,762 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 09:31:42,830 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 09:31:42,831 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 09:49:32,944 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 09:49:32,945 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 09:52:28,891 [INFO] 生成验证码: UK3W
2025-04-14 09:52:29,304 [INFO] 生成验证码: BNTR
2025-04-14 09:52:44,369 [INFO] 验证码比对: 输入=bntr, 存储=bntr
2025-04-14 09:52:44,491 [INFO] 用户 Ron 登录成功
2025-04-14 09:52:44,708 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 09:52:44,709 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 09:55:53,940 [INFO] 生成验证码: CXMB
2025-04-14 09:55:54,224 [INFO] 生成验证码: Z5XJ
2025-04-14 09:56:05,791 [INFO] 验证码比对: 输入=z5xj, 存储=z5xj
2025-04-14 09:56:06,091 [INFO] 生成验证码: U6M4
2025-04-14 09:56:06,160 [INFO] 生成验证码: XPR9
2025-04-14 10:19:00,020 [INFO] 验证码比对: 输入=xpr9, 存储=xpr9
2025-04-14 10:19:00,147 [INFO] 用户 Ron 登录成功
2025-04-14 10:19:00,228 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:19:00,229 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:20:16,049 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:20:16,050 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:36:25,467 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:36:25,467 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:42:40,225 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:42:40,225 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:42:52,267 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:42:52,267 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:42:54,617 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:42:54,618 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:43:25,696 [INFO] File Manager startup
2025-04-14 10:44:29,582 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:44:29,582 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:44:40,090 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:44:40,090 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:44:46,480 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:44:46,480 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:45:05,774 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:45:05,774 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:45:09,026 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:45:09,026 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:45:13,379 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:45:13,379 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:45:19,609 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:45:19,609 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:45:22,445 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:45:22,446 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:47:00,274 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:47:00,274 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:47:03,512 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:47:03,512 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:50:02,648 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 10:50:02,648 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 10:52:07,642 [INFO] 生成验证码: RHS3
2025-04-14 10:52:07,673 [INFO] 生成验证码: SSNG
2025-04-14 10:54:44,390 [INFO] 生成验证码: QXCW
2025-04-14 10:54:45,928 [INFO] 生成验证码: RKUR
2025-04-14 11:20:42,467 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:20:42,467 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:20:48,176 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:20:48,176 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:21:05,313 [INFO] 生成验证码: 4UXT
2025-04-14 11:21:05,531 [INFO] 生成验证码: CQ8E
2025-04-14 11:21:21,565 [INFO] 验证码比对: 输入=cq8e, 存储=cq8e
2025-04-14 11:21:21,686 [INFO] 用户 Ron 登录成功
2025-04-14 11:21:21,900 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:21:21,901 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:21:57,308 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:21:57,309 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:22:00,747 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 11:22:00,752 [INFO] 找到 194 条日志记录
2025-04-14 11:22:25,503 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:22:25,503 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:22:31,236 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:22:31,236 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:22:35,123 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:22:35,123 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:25:43,292 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:25:43,292 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:25:43,640 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:25:43,641 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:25:44,122 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:25:44,122 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:25:49,207 [ERROR] 上传失败: 没有选择文件
2025-04-14 11:26:00,520 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:26:00,520 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:26:05,248 [ERROR] 上传失败: 没有选择文件
2025-04-14 11:26:18,962 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 11:26:18,966 [INFO] 找到 194 条日志记录
2025-04-14 11:26:27,110 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:26:27,110 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:26:56,537 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:26:56,537 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:27:27,481 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:27:27,481 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:27:52,845 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-14 11:27:52,851 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,858 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,866 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,872 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,880 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,886 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,893 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,899 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,906 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,913 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-14 11:27:52,920 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-14 11:27:53,313 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:27:53,313 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:27:56,922 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:27:56,922 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:27:59,124 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:27:59,124 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:00,517 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:00,518 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:00,842 [ERROR] 上传失败: 没有选择文件
2025-04-14 11:28:04,108 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:04,108 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:07,388 [INFO] 开始删除文件夹: Specification
2025-04-14 11:28:07,388 [INFO] 处理文件夹: Specification
2025-04-14 11:28:07,388 [INFO] 文件夹标记为已删除: True
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 11:28:07,391 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 11:28:07,397 [INFO] 数据库更新成功
2025-04-14 11:28:07,810 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:07,811 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:54,752 [INFO] 用户 cv24051 上传文件: 123/331.BMP 到文件夹: 123
2025-04-14 11:28:54,767 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP 到文件夹: 123
2025-04-14 11:28:54,781 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP 到文件夹: 123
2025-04-14 11:28:54,795 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP 到文件夹: 123
2025-04-14 11:28:54,817 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP 到文件夹: 123
2025-04-14 11:28:54,829 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP 到文件夹: 123
2025-04-14 11:28:54,843 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP 到文件夹: 123
2025-04-14 11:28:54,855 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 123
2025-04-14 11:28:55,251 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:55,251 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:58,679 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:58,680 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:28:58,726 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:28:58,726 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:00,875 [ERROR] 上传失败: 没有选择文件
2025-04-14 11:29:01,202 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:01,202 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:04,585 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:04,585 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:11,339 [INFO] 开始删除文件夹: 123
2025-04-14 11:29:11,340 [INFO] 处理文件夹: 123
2025-04-14 11:29:11,340 [INFO] 文件夹标记为已删除: True
2025-04-14 11:29:11,346 [INFO] 文件标记为已删除: 123/331.BMP
2025-04-14 11:29:11,346 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-14 11:29:11,347 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 11:29:11,355 [INFO] 数据库更新成功
2025-04-14 11:29:11,670 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:11,670 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:13,024 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:13,025 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:17,223 [INFO] 开始删除文件夹: Delta
2025-04-14 11:29:17,223 [INFO] 处理文件夹: Delta
2025-04-14 11:29:17,224 [INFO] 文件夹标记为已删除: True
2025-04-14 11:29:17,228 [INFO] 处理文件夹: Specification
2025-04-14 11:29:17,228 [INFO] 文件夹标记为已删除: True
2025-04-14 11:29:17,231 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 11:29:17,231 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 11:29:17,231 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 11:29:17,232 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 11:29:17,236 [INFO] 处理文件夹: 123
2025-04-14 11:29:17,236 [INFO] 文件夹标记为已删除: True
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/331.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-14 11:29:17,238 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-14 11:29:17,239 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 11:29:17,244 [INFO] 数据库更新成功
2025-04-14 11:29:17,647 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:17,647 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:29:59,584 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:29:59,585 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:30:34,596 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:30:34,596 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:30:43,060 [ERROR] 上传失败: 没有选择文件
2025-04-14 11:30:43,151 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:30:43,152 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:30:46,607 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:30:46,607 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:30:48,325 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:30:48,326 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:30:56,175 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 11:30:56,179 [INFO] 找到 221 条日志记录
2025-04-14 11:31:00,383 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:31:00,383 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:31:11,433 [INFO] 用户 cv24051 上传文件: Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 根目录
2025-04-14 11:31:11,747 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:31:11,748 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:31:16,621 [INFO] 开始删除文件: Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 11:31:16,621 [INFO] 文件标记为已删除: True, 删除时间: 2025-04-14 11:31:16.621349
2025-04-14 11:31:16,627 [INFO] 数据库更新成功
2025-04-14 11:31:17,134 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:31:17,134 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:32:41,641 [INFO] 用户 cv24051 上传文件: 工作安排/工作安排.doc 到文件夹: 工作安排
2025-04-14 11:32:41,647 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744601561.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 14, 11, 32, 41, 646961, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 20, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-14 11:32:41,647 [ERROR] 记录用户操作失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744601561.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 14, 11, 32, 41, 646961, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 20, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-14 11:32:42,057 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:32:42,058 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:32:43,900 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:32:43,900 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:32:57,351 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:32:57,351 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:32:59,313 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:32:59,313 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:33:00,780 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:33:00,780 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:33:03,972 [INFO] 开始删除文件夹: 工作安排
2025-04-14 11:33:03,972 [INFO] 处理文件夹: 工作安排
2025-04-14 11:33:03,972 [INFO] 文件夹标记为已删除: True
2025-04-14 11:33:03,975 [INFO] 文件标记为已删除: 工作安排/工作安排.doc
2025-04-14 11:33:03,980 [INFO] 数据库更新成功
2025-04-14 11:33:04,300 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:33:04,300 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:35:44,359 [INFO] 用户 cv24051 上传文件: 工作安排/工作安排.doc 到文件夹: 工作安排
2025-04-14 11:35:44,364 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744601744.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 14, 11, 35, 44, 363750, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 21, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-14 11:35:44,364 [ERROR] 记录用户操作失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': '2025_1744601744.docx', 'original_filename': '工作安排/工作安排2025.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 11400, 'upload_date': datetime.datetime(2025, 4, 14, 11, 35, 44, 363750, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 21, 'user_id': 1, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-14 11:35:44,753 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:35:44,753 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:35:46,844 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:35:46,844 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:35:53,068 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:35:53,068 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:35:56,675 [INFO] 开始删除文件夹: 工作安排
2025-04-14 11:35:56,676 [INFO] 处理文件夹: 工作安排
2025-04-14 11:35:56,676 [INFO] 文件夹标记为已删除: True
2025-04-14 11:35:56,681 [INFO] 文件标记为已删除: 工作安排/工作安排.doc
2025-04-14 11:35:56,687 [INFO] 数据库更新成功
2025-04-14 11:35:57,071 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:35:57,071 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:36:17,407 [INFO] 用户 cv24051 上传文件: 123/331.BMP 到文件夹: 123
2025-04-14 11:36:17,422 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP 到文件夹: 123
2025-04-14 11:36:17,437 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP 到文件夹: 123
2025-04-14 11:36:17,450 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP 到文件夹: 123
2025-04-14 11:36:17,463 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP 到文件夹: 123
2025-04-14 11:36:17,476 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP 到文件夹: 123
2025-04-14 11:36:17,489 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP 到文件夹: 123
2025-04-14 11:36:17,502 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 123
2025-04-14 11:36:17,813 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:36:17,814 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:36:46,910 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:36:46,911 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:36:48,679 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:36:48,679 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:36:52,200 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:36:52,200 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:36:56,374 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:36:56,375 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:37:05,257 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:37:05,257 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:37:09,629 [INFO] 开始删除文件夹: 123
2025-04-14 11:37:09,630 [INFO] 处理文件夹: 123
2025-04-14 11:37:09,630 [INFO] 文件夹标记为已删除: True
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/331.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-14 11:37:09,632 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 11:37:09,637 [INFO] 数据库更新成功
2025-04-14 11:37:10,011 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:37:10,011 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 11:37:34,477 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 11:37:34,478 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 12:06:25,912 [INFO] 生成验证码: 7N4L
2025-04-14 12:06:26,064 [INFO] 生成验证码: 4X53
2025-04-14 12:06:55,487 [INFO] 验证码比对: 输入=4x53, 存储=4x53
2025-04-14 12:06:55,612 [INFO] 用户 Ron 登录成功
2025-04-14 12:06:55,665 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 12:06:55,665 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 12:09:00,562 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744603740', 'original_filename': '微信公众号/微信公众号.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 15775, 'upload_date': datetime.datetime(2025, 4, 14, 12, 9, 0, 558905, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 23, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-14 12:09:00,563 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744603740', 'original_filename': '微信公众号/微信公众号.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 15775, 'upload_date': datetime.datetime(2025, 4, 14, 12, 9, 0, 558905, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 23, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-14 12:09:00,563 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-14 12:09:00,633 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 12:09:00,633 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 12:18:54,243 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744604334', 'original_filename': '领英/领英.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 14647, 'upload_date': datetime.datetime(2025, 4, 14, 12, 18, 54, 240721, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 24, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-14 12:18:54,244 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744604334', 'original_filename': '领英/领英.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 14647, 'upload_date': datetime.datetime(2025, 4, 14, 12, 18, 54, 240721, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 24, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-14 12:18:54,244 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-14 12:18:54,314 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 12:18:54,314 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 12:19:35,336 [ERROR] 保存文件失败: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744604375', 'original_filename': '微信公众号/微信公众号.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 15775, 'upload_date': datetime.datetime(2025, 4, 14, 12, 19, 35, 333306, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 25, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-04-14 12:19:35,337 [ERROR] 保存文件失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (pymysql.err.DataError) (1406, "Data too long for column 'file_type' at row 1")
[SQL: INSERT INTO file (filename, original_filename, file_type, file_size, upload_date, tags, path, folder_id, user_id, is_deleted, delete_time) VALUES (%(filename)s, %(original_filename)s, %(file_type)s, %(file_size)s, %(upload_date)s, %(tags)s, %(path)s, %(folder_id)s, %(user_id)s, %(is_deleted)s, %(delete_time)s)]
[parameters: {'filename': 'docx_1744604375', 'original_filename': '微信公众号/微信公众号.docx', 'file_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'file_size': 15775, 'upload_date': datetime.datetime(2025, 4, 14, 12, 19, 35, 333306, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'tags': None, 'path': '', 'folder_id': 25, 'user_id': 3, 'is_deleted': 0, 'delete_time': None}]
(Background on this error at: https://sqlalche.me/e/20/9h9h) (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-04-14 12:19:35,337 [ERROR] 上传失败: cannot access local variable 'file_path' where it is not associated with a value
2025-04-14 12:19:35,408 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 12:19:35,408 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 12:20:24,007 [INFO] File Manager startup
2025-04-14 12:27:09,597 [INFO] File Manager startup
2025-04-14 13:32:05,783 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:32:05,783 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:32:08,428 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 13:32:08,434 [INFO] 找到 242 条日志记录
2025-04-14 13:35:40,115 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:35:40,115 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:36:22,830 [INFO] File Manager startup
2025-04-14 13:36:26,748 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:36:26,748 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:36:27,955 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:36:27,955 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:36:28,447 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:36:28,447 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:36:32,947 [INFO] 用户 cv24051 上传文件: 331.BMP 到文件夹: 根目录
2025-04-14 13:36:33,136 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:36:33,136 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:36:43,546 [INFO] 开始删除文件: 331.BMP
2025-04-14 13:36:43,546 [INFO] 文件标记为已删除: True, 删除时间: 2025-04-14 13:36:43.546387
2025-04-14 13:36:43,551 [INFO] 数据库更新成功
2025-04-14 13:36:43,743 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:36:43,743 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:43:32,495 [INFO] File Manager startup
2025-04-14 13:44:11,647 [INFO] File Manager startup
2025-04-14 13:44:45,856 [INFO] File Manager startup
2025-04-14 13:46:06,076 [INFO] File Manager startup
2025-04-14 13:48:15,352 [INFO] File Manager startup
2025-04-14 13:49:53,640 [INFO] File Manager startup
2025-04-14 13:50:03,618 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:03,618 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:50:12,594 [INFO] 用户 cv24051 上传文件: 123/331.BMP 到文件夹: 123
2025-04-14 13:50:12,611 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP 到文件夹: 123
2025-04-14 13:50:12,625 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP 到文件夹: 123
2025-04-14 13:50:12,641 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP 到文件夹: 123
2025-04-14 13:50:12,656 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP 到文件夹: 123
2025-04-14 13:50:12,671 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP 到文件夹: 123
2025-04-14 13:50:12,686 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP 到文件夹: 123
2025-04-14 13:50:12,701 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 123
2025-04-14 13:50:12,740 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:12,740 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:50:16,834 [INFO] 开始删除文件夹: 123
2025-04-14 13:50:16,834 [INFO] 处理文件夹: 123
2025-04-14 13:50:16,834 [INFO] 文件夹标记为已删除: True
2025-04-14 13:50:16,840 [INFO] 文件标记为已删除: 123/331.BMP
2025-04-14 13:50:16,840 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-14 13:50:16,840 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-14 13:50:16,841 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-14 13:50:16,841 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-14 13:50:16,841 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-14 13:50:16,841 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-14 13:50:16,841 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 13:50:16,846 [INFO] 数据库更新成功
2025-04-14 13:50:16,871 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:16,871 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:50:43,156 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:43,156 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:50:44,615 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:44,615 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:50:45,277 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:50:45,278 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:51:01,736 [INFO] File Manager startup
2025-04-14 13:51:01,840 [INFO] File Manager startup
2025-04-14 13:51:01,949 [INFO] File Manager startup
2025-04-14 13:51:02,021 [INFO] File Manager startup
2025-04-14 13:51:02,108 [INFO] File Manager startup
2025-04-14 13:51:02,143 [INFO] File Manager startup
2025-04-14 13:51:02,225 [INFO] File Manager startup
2025-04-14 13:51:02,302 [INFO] File Manager startup
2025-04-14 13:51:10,586 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:51:10,586 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:51:22,332 [INFO] 用户 cv24051 上传文件: 123/331.BMP 到文件夹: 123
2025-04-14 13:51:22,346 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP 到文件夹: 123
2025-04-14 13:51:22,359 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP 到文件夹: 123
2025-04-14 13:51:22,373 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP 到文件夹: 123
2025-04-14 13:51:22,386 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP 到文件夹: 123
2025-04-14 13:51:22,399 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP 到文件夹: 123
2025-04-14 13:51:22,411 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP 到文件夹: 123
2025-04-14 13:51:22,423 [INFO] 用户 cv24051 上传文件: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP 到文件夹: 123
2025-04-14 13:51:22,446 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:51:22,446 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:52:40,967 [INFO] 开始删除文件夹: 123
2025-04-14 13:52:40,968 [INFO] 处理文件夹: 123
2025-04-14 13:52:40,968 [INFO] 文件夹标记为已删除: True
2025-04-14 13:52:40,973 [INFO] 文件标记为已删除: 123/331.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111032-383-920313168597.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111125-756-920846897432.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111156-984-921159185118.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111231-808-921507424670.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111330-636-922095702516.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111339-029-922179632411.BMP
2025-04-14 13:52:40,974 [INFO] 文件标记为已删除: 123/Camera MV-GE501GC#AC1FFEC6-Snapshot-20250329-111412-172-922511064158.BMP
2025-04-14 13:52:40,980 [INFO] 数据库更新成功
2025-04-14 13:52:40,999 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:52:40,999 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:53:26,392 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:53:26,393 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:53:38,393 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:53:38,393 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:55:30,002 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:55:30,002 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 13:55:34,429 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 13:55:34,429 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:29:46,367 [INFO] File Manager startup
2025-04-14 14:29:46,368 [INFO] File Manager startup
2025-04-14 14:29:46,466 [INFO] File Manager startup
2025-04-14 14:29:46,532 [INFO] File Manager startup
2025-04-14 14:29:46,613 [INFO] File Manager startup
2025-04-14 14:29:46,632 [INFO] File Manager startup
2025-04-14 14:29:46,691 [INFO] File Manager startup
2025-04-14 14:29:46,705 [INFO] File Manager startup
2025-04-14 14:29:56,443 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:29:56,443 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:30:00,128 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:30:00,129 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:43:48,393 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:43:48,393 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:46:19,242 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:46:19,243 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:46:42,902 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:46:42,903 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:46:43,798 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:46:43,799 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:50:53,750 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:50:53,750 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:50:57,960 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:50:57,960 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:51:00,336 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:51:00,336 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:51:05,983 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:51:05,983 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:51:07,364 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:51:07,364 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:56:40,784 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:56:40,784 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:56:50,660 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:56:50,660 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:56:52,317 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:56:52,317 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:56:56,516 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:56:56,517 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 14:56:59,763 [INFO] File Manager startup
2025-04-14 14:57:06,043 [INFO] 生成验证码: 4YQY
2025-04-14 14:57:06,161 [INFO] 生成验证码: RN8E
2025-04-14 14:57:15,961 [INFO] 用户 夏欢钰 退出登录
2025-04-14 14:57:16,022 [INFO] 生成验证码: E4BZ
2025-04-14 14:57:16,045 [INFO] 生成验证码: 9L99
2025-04-14 14:57:45,070 [INFO] 验证码比对: 输入=9l99, 存储=9l99
2025-04-14 14:57:52,299 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 14:57:52,422 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 14:57:52,460 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 14:57:52,460 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:03:47,999 [INFO] 生成验证码: 5RRD
2025-04-14 15:03:48,068 [INFO] 生成验证码: CDUY
2025-04-14 15:04:05,081 [INFO] 验证码比对: 输入=cduy, 存储=cduy
2025-04-14 15:04:05,201 [INFO] 用户 苏强 登录成功
2025-04-14 15:04:05,416 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:04:05,416 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:04:09,500 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:04:09,500 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:05:12,975 [INFO] 生成验证码: 7ZTR
2025-04-14 15:05:13,165 [INFO] 生成验证码: FGTT
2025-04-14 15:05:38,824 [INFO] 验证码比对: 输入=fgtt, 存储=fgtt
2025-04-14 15:06:01,689 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 15:06:01,806 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 15:06:01,822 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:06:01,822 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:06:09,877 [INFO] 超级管理员 cv24051 授予 苏强 的高级用户权限
2025-04-14 15:06:56,623 [INFO] File Manager startup
2025-04-14 15:08:31,604 [INFO] 超级管理员 cv24051 授予 魏林 的高级用户权限
2025-04-14 15:08:52,956 [INFO] 用户 cv24051 退出登录
2025-04-14 15:08:53,092 [INFO] 生成验证码: FVLC
2025-04-14 15:08:53,124 [INFO] 生成验证码: T5JM
2025-04-14 15:11:07,574 [INFO] 生成验证码: HTLY
2025-04-14 15:11:07,615 [INFO] 生成验证码: ZVD8
2025-04-14 15:11:27,869 [INFO] 验证码比对: 输入=zvd8, 存储=zvd8
2025-04-14 15:11:27,990 [INFO] 用户 彭富强 登录成功
2025-04-14 15:11:28,004 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:11:28,004 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:12:11,183 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:12:11,183 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:14:34,522 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:14:34,522 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:14:57,658 [INFO] 生成验证码: ZTKX
2025-04-14 15:14:57,781 [INFO] 生成验证码: 76TA
2025-04-14 15:27:57,951 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:27:57,952 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:27:58,795 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:27:58,795 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:27:59,249 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:27:59,249 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:27:59,660 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:27:59,660 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:28:02,500 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:28:02,501 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:29:03,524 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:29:03,524 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:29:06,198 [ERROR] 上传失败: 没有选择文件
2025-04-14 15:29:36,901 [INFO] 用户 cv24051 上传文件: 视觉/12111.hdev 到文件夹: 视觉
2025-04-14 15:29:36,908 [INFO] 用户 cv24051 上传文件: 视觉/1212.hdev 到文件夹: 视觉
2025-04-14 15:29:36,914 [INFO] 用户 cv24051 上传文件: 视觉/1212picture.hdev 到文件夹: 视觉
2025-04-14 15:29:36,921 [INFO] 用户 cv24051 上传文件: 视觉/225.cs 到文件夹: 视觉
2025-04-14 15:29:36,926 [INFO] 用户 cv24051 上传文件: 视觉/225.hdev 到文件夹: 视觉
2025-04-14 15:29:36,932 [INFO] 用户 cv24051 上传文件: 视觉/camerate.hdev 到文件夹: 视觉
2025-04-14 15:29:36,938 [INFO] 用户 cv24051 上传文件: 视觉/DirectShow-接口访问相机参数设置方法.pdf 到文件夹: 视觉
2025-04-14 15:29:36,945 [INFO] 用户 cv24051 上传文件: 视觉/HALCON 图像拼接.pdf 到文件夹: 视觉
2025-04-14 15:29:36,956 [INFO] 用户 cv24051 上传文件: 视觉/MindVision工业相机开发手册.pdf 到文件夹: 视觉
2025-04-14 15:29:36,963 [INFO] 用户 cv24051 上传文件: 视觉/picturetest.hdev 到文件夹: 视觉
2025-04-14 15:29:36,970 [INFO] 用户 cv24051 上传文件: 视觉/winding1.zip 到文件夹: 视觉
2025-04-14 15:29:36,977 [INFO] 用户 cv24051 上传文件: 视觉/基础知识----工业镜头.pdf 到文件夹: 视觉
2025-04-14 15:29:36,987 [INFO] 用户 cv24051 上传文件: 视觉/基础知识----机器视觉光源.pdf 到文件夹: 视觉
2025-04-14 15:29:36,995 [INFO] 用户 cv24051 上传文件: 视觉/基础知识----机器视觉工业相机.pdf 到文件夹: 视觉
2025-04-14 15:29:37,002 [INFO] 用户 cv24051 上传文件: 视觉/模板匹配.hdev 到文件夹: 视觉
2025-04-14 15:29:37,008 [INFO] 用户 cv24051 上传文件: 视觉/扁线/225.hdev 到文件夹: 视觉
2025-04-14 15:29:37,014 [INFO] 用户 cv24051 上传文件: 视觉/扁线/34.cs 到文件夹: 视觉
2025-04-14 15:29:37,023 [INFO] 用户 cv24051 上传文件: 视觉/扁线/Image225.BMP 到文件夹: 视觉
2025-04-14 15:29:37,029 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding1.sln 到文件夹: 视觉
2025-04-14 15:29:37,036 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:37,042 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:37,048 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,054 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:37,060 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/MVSDK.cs 到文件夹: 视觉
2025-04-14 15:29:37,066 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/packages.config 到文件夹: 视觉
2025-04-14 15:29:37,072 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:37,078 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:37,084 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:37,090 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,095 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:37,102 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,107 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:37,113 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:37,119 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:37,124 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:37,130 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:37,137 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:37,143 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:37,148 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:37,154 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:37,160 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:37,166 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:37,173 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:37,178 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:37,184 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:37,190 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:37,197 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:37,203 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:37,208 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:37,214 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:37,220 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:37,226 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:37,232 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:37,238 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:37,244 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:37,250 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,256 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:37,262 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,268 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Release/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:37,274 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:37,279 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:37,285 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:37,291 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs 到文件夹: 视觉
2025-04-14 15:29:37,297 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs 到文件夹: 视觉
2025-04-14 15:29:37,303 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs 到文件夹: 视觉
2025-04-14 15:29:37,308 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.CopyComplete 到文件夹: 视觉
2025-04-14 15:29:37,314 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:37,320 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:37,326 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:37,332 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:37,339 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,345 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:37,352 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,357 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:37,364 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:37,369 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:37,375 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:37,381 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:37,387 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,392 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:37,398 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Release/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,404 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:37,410 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:37,417 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:37,422 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:37,428 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,434 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:37,440 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,447 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/winding/.vs/winding.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:37,453 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/packages/NModbus4.2.1.0/.signature.p7s 到文件夹: 视觉
2025-04-14 15:29:37,459 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg 到文件夹: 视觉
2025-04-14 15:29:37,465 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/packages/NModbus4.2.1.0/NModbus4.pdb 到文件夹: 视觉
2025-04-14 15:29:37,470 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:37,477 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:37,483 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:37,489 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:37,495 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:37,501 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:37,507 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:37,513 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:37,519 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:37,527 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:37,532 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/0a018aab-82cb-41e5-a3b6-12c6b4511dd1.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,539 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/1060a8f8-e8bf-4ac2-a6fc-cd9813dfa1fe.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,546 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/53de73bd-4ecd-434e-ad5b-b0c3b21d6be1.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,552 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/9f91fe81-3017-4595-a693-c9ff15e4aee1.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,558 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/c59a85a2-23dc-484d-9f1b-d741f821fde0.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,564 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:37,571 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:37,577 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:37,583 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v17/ResourceExplorer/settings.json 到文件夹: 视觉
2025-04-14 15:29:37,589 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:37,595 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:37,602 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:37,608 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:37,614 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:37,620 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,626 [INFO] 用户 cv24051 上传文件: 视觉/扁线/winding1/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx 到文件夹: 视觉
2025-04-14 15:29:37,631 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding1.sln 到文件夹: 视觉
2025-04-14 15:29:37,638 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:37,644 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:37,650 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,656 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:37,663 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/MVSDK.cs 到文件夹: 视觉
2025-04-14 15:29:37,669 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/packages.config 到文件夹: 视觉
2025-04-14 15:29:37,675 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:37,681 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:37,686 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:37,692 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,699 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:37,705 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:37,711 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:37,717 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:37,722 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:37,729 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:37,735 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:37,741 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:37,747 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:37,754 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:37,761 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:37,767 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:37,772 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:37,778 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:37,784 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:37,790 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:37,796 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:37,803 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:37,809 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:37,815 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:37,821 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:37,827 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:37,833 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:37,839 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:37,845 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:37,852 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:37,857 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,863 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:37,870 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,875 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Release/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:37,882 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:37,889 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:37,897 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:37,904 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs 到文件夹: 视觉
2025-04-14 15:29:37,909 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs 到文件夹: 视觉
2025-04-14 15:29:37,915 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs 到文件夹: 视觉
2025-04-14 15:29:37,921 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.CopyComplete 到文件夹: 视觉
2025-04-14 15:29:37,927 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:37,934 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:37,939 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:37,945 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:37,952 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:37,958 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:37,964 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:37,969 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:37,977 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:37,982 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:37,988 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:37,994 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:38,000 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,006 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:38,011 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Release/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,018 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:38,026 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/halcondotnet.xml 到文件夹: 视觉
2025-04-14 15:29:38,033 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:38,040 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:38,046 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:38,051 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,058 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:38,064 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,070 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/winding/.vs/winding.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:38,076 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/.signature.p7s 到文件夹: 视觉
2025-04-14 15:29:38,082 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg 到文件夹: 视觉
2025-04-14 15:29:38,089 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/NModbus4.pdb 到文件夹: 视觉
2025-04-14 15:29:38,094 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:38,100 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:38,106 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:38,112 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:38,118 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:38,124 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:38,130 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:38,136 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:38,144 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:38,150 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/0a018aab-82cb-41e5-a3b6-12c6b4511dd1.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,156 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/1060a8f8-e8bf-4ac2-a6fc-cd9813dfa1fe.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,162 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/53de73bd-4ecd-434e-ad5b-b0c3b21d6be1.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,168 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/9f91fe81-3017-4595-a693-c9ff15e4aee1.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,173 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/c59a85a2-23dc-484d-9f1b-d741f821fde0.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,180 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:38,185 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:38,191 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:38,197 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v17/ResourceExplorer/settings.json 到文件夹: 视觉
2025-04-14 15:29:38,203 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:38,210 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:38,216 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:38,221 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:38,227 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:38,233 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,239 [INFO] 用户 cv24051 上传文件: 视觉/winding1去除小矛刺/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,245 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding1.sln 到文件夹: 视觉
2025-04-14 15:29:38,251 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:38,257 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:38,264 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,271 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:38,277 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/MVSDK.cs 到文件夹: 视觉
2025-04-14 15:29:38,283 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/packages.config 到文件夹: 视觉
2025-04-14 15:29:38,289 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:38,295 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:38,302 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:38,308 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,314 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:38,321 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,326 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:38,333 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:38,339 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:38,345 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:38,350 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:38,357 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:38,364 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:38,370 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:38,376 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:38,382 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:38,387 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:38,394 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:38,399 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:38,405 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:38,411 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:38,417 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:38,424 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:38,430 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:38,436 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:38,442 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs 到文件夹: 视觉
2025-04-14 15:29:38,447 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs 到文件夹: 视觉
2025-04-14 15:29:38,453 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs 到文件夹: 视觉
2025-04-14 15:29:38,459 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:38,465 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:38,471 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:38,476 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:38,482 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:38,489 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:38,495 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,500 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:38,506 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,511 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:38,520 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:38,528 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/halcondotnet.xml 到文件夹: 视觉
2025-04-14 15:29:38,534 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:38,539 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:38,545 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:38,551 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,558 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:38,563 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,569 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/winding/.vs/winding.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:38,575 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/packages/NModbus4.2.1.0/.signature.p7s 到文件夹: 视觉
2025-04-14 15:29:38,581 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg 到文件夹: 视觉
2025-04-14 15:29:38,610 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/packages/NModbus4.2.1.0/NModbus4.pdb 到文件夹: 视觉
2025-04-14 15:29:38,618 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:38,624 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:38,629 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:38,635 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:38,642 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:38,648 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:38,654 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:38,660 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:38,665 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:38,672 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:38,677 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/73e80f76-7cb3-433e-b42e-8a6708743c36.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,683 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/a7641ce0-b900-4fd3-8960-4e2d726cd4d1.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,689 [INFO] 用户 cv24051 上传文件: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/c8909e3b-ad09-4dc4-a054-a25631b409ba.vsidx 到文件夹: 视觉
2025-04-14 15:29:38,694 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding.sln 到文件夹: 视觉
2025-04-14 15:29:38,702 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:38,708 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:38,714 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,720 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:38,726 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:38,733 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:38,739 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:38,744 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,750 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:38,756 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:38,762 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:38,768 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:38,774 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:38,779 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:38,785 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:38,790 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:38,796 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:38,802 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:38,808 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:38,814 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:38,820 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:38,826 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:38,832 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:38,838 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:38,843 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:38,849 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:38,855 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:38,863 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:38,868 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:38,874 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:38,880 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:38,886 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:38,893 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:38,899 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:38,904 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,910 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:38,916 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,922 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:38,938 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/halcon.dll 到文件夹: 视觉
2025-04-14 15:29:38,945 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:38,954 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/hcanvas.dll 到文件夹: 视觉
2025-04-14 15:29:38,959 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:38,966 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:38,972 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:38,978 [INFO] 用户 cv24051 上传文件: 视觉/winding/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:38,984 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:38,991 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:38,996 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:39,002 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/v17/ResourceExplorer/settings.json 到文件夹: 视觉
2025-04-14 15:29:39,008 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/FileContentIndex/d0a1bf71-0010-4f26-8595-d21665cc733a.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,014 [INFO] 用户 cv24051 上传文件: 视觉/winding/.vs/winding/FileContentIndex/efaca3b5-f7dd-4123-9f8c-628f5e14dc46.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,021 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1.rar 到文件夹: 视觉
2025-04-14 15:29:39,027 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding1.sln 到文件夹: 视觉
2025-04-14 15:29:39,032 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:39,038 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:39,044 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,050 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:39,056 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/MVSDK.cs 到文件夹: 视觉
2025-04-14 15:29:39,063 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:39,068 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:39,074 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:39,080 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,087 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:39,092 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,099 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:39,104 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:39,110 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:39,117 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:39,123 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:39,128 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:39,134 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:39,140 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:39,146 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:39,152 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:39,157 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:39,163 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:39,168 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:39,175 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:39,180 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:39,186 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:39,192 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:39,197 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:39,203 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:39,209 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs 到文件夹: 视觉
2025-04-14 15:29:39,215 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs 到文件夹: 视觉
2025-04-14 15:29:39,221 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs 到文件夹: 视觉
2025-04-14 15:29:39,226 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:39,232 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:39,240 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:39,245 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:39,251 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:39,256 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:39,262 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:39,268 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:39,274 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:39,279 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:39,286 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:39,291 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:39,298 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:39,303 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:39,308 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:39,314 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/winding/.vs/winding.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:39,319 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:39,325 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:39,332 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:39,338 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:39,343 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:39,349 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:39,354 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:39,362 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:39,368 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/3380fe03-0992-4d70-9514-b4072cf40ceb.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,373 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/47d2ae4e-82aa-425c-9ba0-9d56687aff39.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,379 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/6b87b3f2-9ac2-4119-b92e-e4af1cb7476a.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,385 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/9b50e811-1385-4895-8a24-d56322ac9cb2.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,392 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/ab9b5fe7-75cc-418b-a6de-32ce505577f1.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,397 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/bcd4a255-cebd-4e2b-883f-1514d4db894a.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,403 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:39,409 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:39,415 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:39,420 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v17/ResourceExplorer/settings.json 到文件夹: 视觉
2025-04-14 15:29:39,427 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:39,433 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:39,439 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:39,446 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:39,452 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:39,459 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,464 [INFO] 用户 cv24051 上传文件: 视觉/mdws/winding1/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,470 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/app.config 到文件夹: 视觉
2025-04-14 15:29:39,476 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:39,482 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,489 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:39,495 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Halcon.csproj 到文件夹: 视觉
2025-04-14 15:29:39,500 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Program.cs 到文件夹: 视觉
2025-04-14 15:29:39,506 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/app.manifest 到文件夹: 视觉
2025-04-14 15:29:39,512 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:39,519 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,524 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:39,531 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,536 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:39,542 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:39,548 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:39,554 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:39,559 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:39,565 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:39,572 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:39,580 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:39,586 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.exe 到文件夹: 视觉
2025-04-14 15:29:39,592 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:39,597 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.pdb 到文件夹: 视觉
2025-04-14 15:29:39,603 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:39,610 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/x86/Debug/TempPE/Properties.Resources.Designer.cs.dll 到文件夹: 视觉
2025-04-14 15:29:39,616 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:39,622 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:39,627 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:39,634 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.CopyComplete 到文件夹: 视觉
2025-04-14 15:29:39,639 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:39,645 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:39,651 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:39,657 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:39,663 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.exe 到文件夹: 视觉
2025-04-14 15:29:39,669 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:39,675 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.pdb 到文件夹: 视觉
2025-04-14 15:29:39,681 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/Halcon.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:39,687 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/obj/Debug/TempPE/Properties.Resources.Designer.cs.dll 到文件夹: 视觉
2025-04-14 15:29:39,695 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/42264375.bmp 到文件夹: 视觉
2025-04-14 15:29:39,701 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/Halcon.exe 到文件夹: 视觉
2025-04-14 15:29:39,708 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/Halcon.exe.config 到文件夹: 视觉
2025-04-14 15:29:39,713 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/Halcon.pdb 到文件夹: 视觉
2025-04-14 15:29:39,720 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:39,726 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:39,731 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/bin/Debug/MVSDK.pdb 到文件夹: 视觉
2025-04-14 15:29:39,738 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:39,744 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:39,750 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:39,756 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:39,762 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:39,767 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:39,774 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:39,780 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/FileContentIndex/438c6f89-a71f-4c3a-b8e0-a5f7b4d65fe0.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,786 [INFO] 用户 cv24051 上传文件: 视觉/mdws/Halcon/.vs/Halcon/FileContentIndex/f86cdd57-8744-4815-baaf-45462a6fb29d.vsidx 到文件夹: 视觉
2025-04-14 15:29:39,792 [INFO] 用户 cv24051 上传文件: 视觉/code/数据增强.py 到文件夹: 视觉
2025-04-14 15:29:39,797 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding1.sln 到文件夹: 视觉
2025-04-14 15:29:39,804 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/App.config 到文件夹: 视觉
2025-04-14 15:29:39,810 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Form1.cs 到文件夹: 视觉
2025-04-14 15:29:39,816 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Form1.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,822 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Form1.resx 到文件夹: 视觉
2025-04-14 15:29:39,829 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/MVSDK.cs 到文件夹: 视觉
2025-04-14 15:29:39,835 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/packages.config 到文件夹: 视觉
2025-04-14 15:29:39,841 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Program.cs 到文件夹: 视觉
2025-04-14 15:29:39,847 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/winding.csproj 到文件夹: 视觉
2025-04-14 15:29:39,852 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Properties/AssemblyInfo.cs 到文件夹: 视觉
2025-04-14 15:29:39,858 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Properties/Resources.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,865 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Properties/Resources.resx 到文件夹: 视觉
2025-04-14 15:29:39,871 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Properties/Settings.Designer.cs 到文件夹: 视觉
2025-04-14 15:29:39,877 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/Properties/Settings.settings 到文件夹: 视觉
2025-04-14 15:29:39,883 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/cards.ico 到文件夹: 视觉
2025-04-14 15:29:39,890 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/dragicon.ico 到文件夹: 视觉
2025-04-14 15:29:39,896 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/ip.ico 到文件夹: 视觉
2025-04-14 15:29:39,901 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/myico.ico 到文件夹: 视觉
2025-04-14 15:29:39,907 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/sample.ico 到文件夹: 视觉
2025-04-14 15:29:39,913 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/sashtest.ico 到文件夹: 视觉
2025-04-14 15:29:39,920 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/toolchar.ico 到文件夹: 视觉
2025-04-14 15:29:39,926 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/toolgame.ico 到文件夹: 视觉
2025-04-14 15:29:39,931 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (106).ico 到文件夹: 视觉
2025-04-14 15:29:39,937 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (108).ico 到文件夹: 视觉
2025-04-14 15:29:39,943 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (168).ico 到文件夹: 视觉
2025-04-14 15:29:39,950 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (18).ico 到文件夹: 视觉
2025-04-14 15:29:39,957 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (32).ico 到文件夹: 视觉
2025-04-14 15:29:39,963 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (7).ico 到文件夹: 视觉
2025-04-14 15:29:39,969 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/图标 (96).ico 到文件夹: 视觉
2025-04-14 15:29:39,974 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs 到文件夹: 视觉
2025-04-14 15:29:39,981 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache 到文件夹: 视觉
2025-04-14 15:29:39,987 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache 到文件夹: 视觉
2025-04-14 15:29:39,992 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs 到文件夹: 视觉
2025-04-14 15:29:39,998 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs 到文件夹: 视觉
2025-04-14 15:29:40,004 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs 到文件夹: 视觉
2025-04-14 15:29:40,011 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csproj.AssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:40,016 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache 到文件夹: 视觉
2025-04-14 15:29:40,022 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csproj.FileListAbsolute.txt 到文件夹: 视觉
2025-04-14 15:29:40,028 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csproj.GenerateResource.cache 到文件夹: 视觉
2025-04-14 15:29:40,033 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csproj.Up2Date 到文件夹: 视觉
2025-04-14 15:29:40,040 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.csprojAssemblyReference.cache 到文件夹: 视觉
2025-04-14 15:29:40,045 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:40,052 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.Form1.resources 到文件夹: 视觉
2025-04-14 15:29:40,058 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:40,064 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/obj/Debug/winding.Properties.Resources.resources 到文件夹: 视觉
2025-04-14 15:29:40,072 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/halcondotnet.dll 到文件夹: 视觉
2025-04-14 15:29:40,079 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/halcondotnet.xml 到文件夹: 视觉
2025-04-14 15:29:40,085 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/MVSDK.dll 到文件夹: 视觉
2025-04-14 15:29:40,091 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:40,098 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:40,104 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/winding.exe 到文件夹: 视觉
2025-04-14 15:29:40,111 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/winding.exe.config 到文件夹: 视觉
2025-04-14 15:29:40,117 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/bin/Debug/winding.pdb 到文件夹: 视觉
2025-04-14 15:29:40,122 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/winding/.vs/winding.csproj.dtbcache.json 到文件夹: 视觉
2025-04-14 15:29:40,128 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/packages/NModbus4.2.1.0/.signature.p7s 到文件夹: 视觉
2025-04-14 15:29:40,135 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg 到文件夹: 视觉
2025-04-14 15:29:40,141 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/packages/NModbus4.2.1.0/NModbus4.pdb 到文件夹: 视觉
2025-04-14 15:29:40,147 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll 到文件夹: 视觉
2025-04-14 15:29:40,152 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml 到文件夹: 视觉
2025-04-14 15:29:40,159 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:40,165 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:40,171 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:40,176 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:40,181 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:40,187 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:40,193 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:40,201 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:40,208 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/FileContentIndex/0d2b72e4-59c2-41da-acab-979e0403a3c2.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,213 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/FileContentIndex/4fcfe066-2a77-4be4-9a1f-e3f53756c02d.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,219 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/FileContentIndex/6b87b3f2-9ac2-4119-b92e-e4af1cb7476a.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,225 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/FileContentIndex/70341e1b-a2a1-4d59-bbbf-684cce0ad144.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,231 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding1/FileContentIndex/90da6794-2f4a-4993-9398-81a71aee6d93.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,237 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v17/.suo 到文件夹: 视觉
2025-04-14 15:29:40,242 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v17/DocumentLayout.backup.json 到文件夹: 视觉
2025-04-14 15:29:40,248 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v17/DocumentLayout.json 到文件夹: 视觉
2025-04-14 15:29:40,253 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v17/ResourceExplorer/settings.json 到文件夹: 视觉
2025-04-14 15:29:40,261 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v15/.suo 到文件夹: 视觉
2025-04-14 15:29:40,266 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/db.lock 到文件夹: 视觉
2025-04-14 15:29:40,272 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide 到文件夹: 视觉
2025-04-14 15:29:40,277 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide-shm 到文件夹: 视觉
2025-04-14 15:29:40,284 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide-wal 到文件夹: 视觉
2025-04-14 15:29:40,291 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx 到文件夹: 视觉
2025-04-14 15:29:40,297 [INFO] 用户 cv24051 上传文件: 视觉/227GPIO/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx 到文件夹: 视觉
2025-04-14 15:29:41,113 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:29:41,113 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:29:45,273 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:29:45,273 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:19,086 [ERROR] 预览文件失败: 'file_id' is undefined
2025-04-14 15:30:19,262 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:19,263 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:22,063 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:22,063 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:23,117 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:23,117 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:41,858 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:41,859 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:46,647 [INFO] 开始删除文件夹: 视觉
2025-04-14 15:30:46,647 [INFO] 处理文件夹: 视觉
2025-04-14 15:30:46,647 [INFO] 文件夹标记为已删除: True
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/12111.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/1212.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/1212picture.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/225.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/225.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/camerate.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/DirectShow-接口访问相机参数设置方法.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/HALCON 图像拼接.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/MindVision工业相机开发手册.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/picturetest.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/winding1.zip
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/基础知识----工业镜头.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/基础知识----机器视觉光源.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/基础知识----机器视觉工业相机.pdf
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/模板匹配.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/225.hdev
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/34.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/Image225.BMP
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding1.sln
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/App.config
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Form1.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Form1.Designer.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Form1.resx
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/MVSDK.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/packages.config
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Program.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/winding.csproj
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Properties/Resources.resx
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/Properties/Settings.settings
2025-04-14 15:30:46,660 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/cards.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/dragicon.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/ip.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/myico.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/sample.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/sashtest.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/toolchar.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/toolgame.ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (106).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (108).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (168).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (18).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (32).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (7).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/图标 (96).ico
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.csproj.Up2Date
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.exe
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.Form1.resources
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.pdb
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Release/winding.Properties.Resources.resources
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.CopyComplete
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.csprojAssemblyReference.cache
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,661 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/halcondotnet.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/MVSDK.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/NModbus4.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/NModbus4.xml
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/winding.exe
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/winding.exe.config
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Release/winding.pdb
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/NModbus4.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/NModbus4.xml
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/winding/.vs/winding.csproj.dtbcache.json
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/packages/NModbus4.2.1.0/.signature.p7s
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/packages/NModbus4.2.1.0/NModbus4.pdb
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v17/.suo
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v17/DocumentLayout.json
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v15/.suo
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/0a018aab-82cb-41e5-a3b6-12c6b4511dd1.vsidx
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/1060a8f8-e8bf-4ac2-a6fc-cd9813dfa1fe.vsidx
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/53de73bd-4ecd-434e-ad5b-b0c3b21d6be1.vsidx
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/9f91fe81-3017-4595-a693-c9ff15e4aee1.vsidx
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding1/FileContentIndex/c59a85a2-23dc-484d-9f1b-d741f821fde0.vsidx
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v17/.suo
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,662 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v17/DocumentLayout.json
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v17/ResourceExplorer/settings.json
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v15/.suo
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/扁线/winding1/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding1.sln
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/App.config
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Form1.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Form1.Designer.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Form1.resx
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/MVSDK.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/packages.config
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Program.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/winding.csproj
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Properties/Resources.resx
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/Properties/Settings.settings
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/cards.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/dragicon.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/ip.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/myico.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/sample.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/sashtest.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/toolchar.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/toolgame.ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (106).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (108).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (168).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (18).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (32).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (7).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/图标 (96).ico
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,663 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.csproj.Up2Date
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.exe
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.Form1.resources
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.pdb
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Release/winding.Properties.Resources.resources
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.CopyComplete
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.csprojAssemblyReference.cache
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/halcondotnet.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/MVSDK.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/NModbus4.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/NModbus4.xml
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/winding.exe
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/winding.exe.config
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Release/winding.pdb
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/halcondotnet.xml
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/NModbus4.dll
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/NModbus4.xml
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,664 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/winding/.vs/winding.csproj.dtbcache.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/.signature.p7s
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/NModbus4.pdb
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v17/.suo
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v17/DocumentLayout.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v15/.suo
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/0a018aab-82cb-41e5-a3b6-12c6b4511dd1.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/1060a8f8-e8bf-4ac2-a6fc-cd9813dfa1fe.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/53de73bd-4ecd-434e-ad5b-b0c3b21d6be1.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/9f91fe81-3017-4595-a693-c9ff15e4aee1.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding1/FileContentIndex/c59a85a2-23dc-484d-9f1b-d741f821fde0.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v17/.suo
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v17/DocumentLayout.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v17/ResourceExplorer/settings.json
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v15/.suo
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1去除小矛刺/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding1.sln
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/App.config
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Form1.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Form1.Designer.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Form1.resx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/MVSDK.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/packages.config
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Program.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/winding.csproj
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Properties/Resources.resx
2025-04-14 15:30:46,665 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/Properties/Settings.settings
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/cards.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/dragicon.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/ip.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/myico.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/sample.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/sashtest.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/toolchar.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/toolgame.ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (106).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (108).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (168).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (18).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (32).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (7).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/图标 (96).ico
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csproj.Up2Date
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.csprojAssemblyReference.cache
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/halcondotnet.xml
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/NModbus4.dll
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/NModbus4.xml
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,666 [INFO] 文件标记为已删除: 视觉/winding1GPIO/winding/.vs/winding.csproj.dtbcache.json
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/packages/NModbus4.2.1.0/.signature.p7s
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/packages/NModbus4.2.1.0/NModbus4.pdb
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v17/.suo
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v17/DocumentLayout.json
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v15/.suo
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/73e80f76-7cb3-433e-b42e-8a6708743c36.vsidx
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/a7641ce0-b900-4fd3-8960-4e2d726cd4d1.vsidx
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding1GPIO/.vs/winding1/FileContentIndex/c8909e3b-ad09-4dc4-a054-a25631b409ba.vsidx
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding.sln
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/App.config
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Form1.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Form1.Designer.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Form1.resx
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Program.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/winding.csproj
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Properties/Resources.resx
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/Properties/Settings.settings
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/cards.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/dragicon.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/ip.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/myico.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/sample.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/sashtest.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/toolchar.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/toolgame.ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (106).ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (108).ico
2025-04-14 15:30:46,667 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (168).ico
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (18).ico
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (32).ico
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (7).ico
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/图标 (96).ico
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.csproj.Up2Date
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/halcon.dll
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/hcanvas.dll
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/v17/.suo
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/v17/DocumentLayout.json
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/v17/ResourceExplorer/settings.json
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/FileContentIndex/d0a1bf71-0010-4f26-8595-d21665cc733a.vsidx
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/winding/.vs/winding/FileContentIndex/efaca3b5-f7dd-4123-9f8c-628f5e14dc46.vsidx
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1.rar
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding1.sln
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/App.config
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Form1.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Form1.Designer.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Form1.resx
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/MVSDK.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Program.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/winding.csproj
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,668 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Properties/Resources.resx
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/Properties/Settings.settings
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/cards.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/dragicon.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/ip.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/myico.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/sample.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/sashtest.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/toolchar.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/toolgame.ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (106).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (108).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (168).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (18).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (32).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (7).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/图标 (96).ico
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csproj.Up2Date
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.csprojAssemblyReference.cache
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,669 [INFO] 文件标记为已删除: 视觉/mdws/winding1/winding/.vs/winding.csproj.dtbcache.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v17/.suo
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v17/DocumentLayout.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v15/.suo
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/3380fe03-0992-4d70-9514-b4072cf40ceb.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/47d2ae4e-82aa-425c-9ba0-9d56687aff39.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/6b87b3f2-9ac2-4119-b92e-e4af1cb7476a.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/9b50e811-1385-4895-8a24-d56322ac9cb2.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/ab9b5fe7-75cc-418b-a6de-32ce505577f1.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding1/FileContentIndex/bcd4a255-cebd-4e2b-883f-1514d4db894a.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v17/.suo
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v17/DocumentLayout.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v17/ResourceExplorer/settings.json
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v15/.suo
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/winding1/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/app.config
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Form1.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Form1.Designer.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Form1.resx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Halcon.csproj
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Program.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/app.manifest
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/Resources.Designer.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/Resources.resx
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/Settings.Designer.cs
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/Properties/Settings.settings
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.AssemblyReference.cache
2025-04-14 15:30:46,670 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.GenerateResource.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.csproj.Up2Date
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.exe
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.Form1.resources
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.pdb
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/Halcon.Properties.Resources.resources
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/x86/Debug/TempPE/Properties.Resources.Designer.cs.dll
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/.AssemblyReference.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.CopyComplete
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.csproj.GenerateResource.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.csprojAssemblyReference.cache
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.exe
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.Form1.resources
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.pdb
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/Halcon.Properties.Resources.resources
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/obj/Debug/TempPE/Properties.Resources.Designer.cs.dll
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/42264375.bmp
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/Halcon.exe
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/Halcon.exe.config
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/Halcon.pdb
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/bin/Debug/MVSDK.pdb
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon.csproj.dtbcache.json
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v17/.suo
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v15/.suo
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/FileContentIndex/438c6f89-a71f-4c3a-b8e0-a5f7b4d65fe0.vsidx
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/mdws/Halcon/.vs/Halcon/FileContentIndex/f86cdd57-8744-4815-baaf-45462a6fb29d.vsidx
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/code/数据增强.py
2025-04-14 15:30:46,671 [INFO] 文件标记为已删除: 视觉/227GPIO/winding1.sln
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/App.config
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Form1.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Form1.Designer.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Form1.resx
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/MVSDK.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/packages.config
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Program.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/winding.csproj
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Properties/AssemblyInfo.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Properties/Resources.Designer.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Properties/Resources.resx
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Properties/Settings.Designer.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/Properties/Settings.settings
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/cards.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/dragicon.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/ip.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/myico.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/sample.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/sashtest.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/toolchar.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/toolgame.ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (106).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (108).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (168).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (18).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (32).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (7).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/图标 (96).ico
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferences.cache
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csproj.AssemblyReference.cache
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csproj.CoreCompileInputs.cache
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csproj.FileListAbsolute.txt
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csproj.GenerateResource.cache
2025-04-14 15:30:46,672 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csproj.Up2Date
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.csprojAssemblyReference.cache
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.exe
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.Form1.resources
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.pdb
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/obj/Debug/winding.Properties.Resources.resources
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/halcondotnet.dll
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/halcondotnet.xml
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/MVSDK.dll
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/NModbus4.dll
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/NModbus4.xml
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/winding.exe
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/winding.exe.config
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/bin/Debug/winding.pdb
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/winding/.vs/winding.csproj.dtbcache.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/packages/NModbus4.2.1.0/.signature.p7s
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/packages/NModbus4.2.1.0/NModbus4.2.1.0.nupkg
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/packages/NModbus4.2.1.0/NModbus4.pdb
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.dll
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/packages/NModbus4.2.1.0/lib/net40/NModbus4.xml
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v17/.suo
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v17/DocumentLayout.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v15/.suo
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/FileContentIndex/0d2b72e4-59c2-41da-acab-979e0403a3c2.vsidx
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/FileContentIndex/4fcfe066-2a77-4be4-9a1f-e3f53756c02d.vsidx
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/FileContentIndex/6b87b3f2-9ac2-4119-b92e-e4af1cb7476a.vsidx
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/FileContentIndex/70341e1b-a2a1-4d59-bbbf-684cce0ad144.vsidx
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding1/FileContentIndex/90da6794-2f4a-4993-9398-81a71aee6d93.vsidx
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v17/.suo
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v17/DocumentLayout.backup.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v17/DocumentLayout.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v17/ResourceExplorer/settings.json
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v15/.suo
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/db.lock
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide
2025-04-14 15:30:46,673 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide-shm
2025-04-14 15:30:46,674 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/v15/Server/sqlite3/storage.ide-wal
2025-04-14 15:30:46,674 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/FileContentIndex/5d12b702-4257-47ed-93eb-116e4a87c6f9.vsidx
2025-04-14 15:30:46,674 [INFO] 文件标记为已删除: 视觉/227GPIO/.vs/winding/FileContentIndex/9ad74093-70ae-427b-b8bf-3e1ecbc62ae5.vsidx
2025-04-14 15:30:46,710 [INFO] 数据库更新成功
2025-04-14 15:30:46,889 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:46,889 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:30:52,844 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:30:52,845 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:31:01,234 [INFO] 开始删除文件夹: Specification
2025-04-14 15:31:01,235 [INFO] 处理文件夹: Specification
2025-04-14 15:31:01,235 [INFO] 文件夹标记为已删除: True
2025-04-14 15:31:01,236 [INFO] 用户 cv24051 删除文件: YR-0601 三层TPEE发热线.pdf
2025-04-14 15:31:01,238 [INFO] 开始删除文件夹: Specification
2025-04-14 15:31:01,238 [INFO] 处理文件夹: Specification
2025-04-14 15:31:01,239 [INFO] 文件夹标记为已删除: True
2025-04-14 15:31:01,241 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 15:31:01,241 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 15:31:01,242 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 15:31:01,243 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 15:31:01,249 [INFO] 数据库更新成功
2025-04-14 15:31:01,250 [INFO] 数据库更新成功
2025-04-14 15:31:01,441 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:31:01,441 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:31:16,134 [INFO] 用户 cv24051 上传文件: Specification/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf 到文件夹: Specification
2025-04-14 15:31:16,143 [INFO] 用户 cv24051 上传文件: Specification/Specification.zip 到文件夹: Specification
2025-04-14 15:31:16,150 [INFO] 用户 cv24051 上传文件: Specification/xlsx 到文件夹: Specification
2025-04-14 15:31:16,156 [INFO] 用户 cv24051 上传文件: Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-14 15:31:16,163 [INFO] 用户 cv24051 上传文件: Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,169 [INFO] 用户 cv24051 上传文件: Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,175 [INFO] 用户 cv24051 上传文件: Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,180 [INFO] 用户 cv24051 上传文件: Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,186 [INFO] 用户 cv24051 上传文件: Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,194 [INFO] 用户 cv24051 上传文件: Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,201 [INFO] 用户 cv24051 上传文件: Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,206 [INFO] 用户 cv24051 上传文件: Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,212 [INFO] 用户 cv24051 上传文件: Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,218 [INFO] 用户 cv24051 上传文件: Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,225 [INFO] 用户 cv24051 上传文件: Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,231 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0416 双层PA发热线cs.pdf 到文件夹: Specification
2025-04-14 15:31:16,237 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0601 三层TPEE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,243 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0655 三层PE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,249 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0675 双层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,256 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0838 单层PA发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,261 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0842 双层PVC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,267 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0933 单层ETFE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,273 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0935 双层PP发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,279 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0973 双层PE发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,285 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0978 双层NTC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,291 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0983 单层PVC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,297 [INFO] 用户 cv24051 上传文件: Specification/Specification/YR-0986 三层NTC发热线.pdf 到文件夹: Specification
2025-04-14 15:31:16,500 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:31:16,500 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:31:17,886 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:31:17,886 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:31:22,937 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:31:22,937 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:31:29,739 [INFO] 开始删除文件夹: Specification
2025-04-14 15:31:29,739 [INFO] 处理文件夹: Specification
2025-04-14 15:31:29,739 [INFO] 文件夹标记为已删除: True
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/Specification.zip
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/xlsx
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/YR-0655 三层PE发热线.pdf
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 15:31:29,743 [INFO] 文件标记为已删除: Specification/YR-0838 单层PA发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0935 双层PP发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0973 双层PE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0416 双层PA发热线cs.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0601 三层TPEE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0655 三层PE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0675 双层ETFE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0838 单层PA发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0842 双层PVC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0933 单层ETFE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0935 双层PP发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0973 双层PE发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0978 双层NTC发热线.pdf
2025-04-14 15:31:29,744 [INFO] 文件标记为已删除: Specification/Specification/YR-0983 单层PVC发热线.pdf
2025-04-14 15:31:29,745 [INFO] 文件标记为已删除: Specification/Specification/YR-0986 三层NTC发热线.pdf
2025-04-14 15:31:29,759 [INFO] 数据库更新成功
2025-04-14 15:31:29,963 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:31:29,964 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:35:51,801 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:35:51,801 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:35:54,089 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:35:54,090 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:49:45,727 [INFO] 生成验证码: ECUQ
2025-04-14 15:49:45,770 [INFO] 生成验证码: 8AH5
2025-04-14 15:49:59,854 [INFO] File Manager startup
2025-04-14 15:49:59,901 [INFO] File Manager startup
2025-04-14 15:49:59,928 [INFO] File Manager startup
2025-04-14 15:50:00,054 [INFO] File Manager startup
2025-04-14 15:50:00,136 [INFO] File Manager startup
2025-04-14 15:50:00,167 [INFO] File Manager startup
2025-04-14 15:50:00,190 [INFO] File Manager startup
2025-04-14 15:50:00,290 [INFO] File Manager startup
2025-04-14 15:50:01,197 [INFO] 验证码比对: 输入=8ah5, 存储=8ah5
2025-04-14 15:50:01,328 [INFO] 用户 夏欢钰 登录成功
2025-04-14 15:50:01,345 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:01,345 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:13,387 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:13,387 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:15,567 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:15,567 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:15,874 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:15,874 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:25,123 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:25,123 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:26,955 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:26,956 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:29,198 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:29,198 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:33,700 [INFO] 开始删除文件夹: cs
2025-04-14 15:50:33,700 [INFO] 处理文件夹: cs
2025-04-14 15:50:33,700 [INFO] 文件夹标记为已删除: True
2025-04-14 15:50:33,703 [INFO] 文件标记为已删除: 20250320_092517.png
2025-04-14 15:50:33,705 [INFO] 处理文件夹: cs
2025-04-14 15:50:33,705 [INFO] 文件夹标记为已删除: True
2025-04-14 15:50:33,706 [INFO] 文件标记为已删除: Python.pdf
2025-04-14 15:50:33,706 [INFO] 文件标记为已删除: Python_.pdf
2025-04-14 15:50:33,706 [INFO] 文件标记为已删除: pythondjango.pdf
2025-04-14 15:50:33,710 [INFO] 数据库更新成功
2025-04-14 15:50:33,909 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:33,910 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:35,197 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:35,197 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:39,190 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:39,191 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:42,636 [INFO] 开始删除文件夹: Specification
2025-04-14 15:50:42,636 [INFO] 处理文件夹: Specification
2025-04-14 15:50:42,636 [INFO] 文件夹标记为已删除: True
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: 1_Programming-PyTorch-for-Deep-Learning.Creating-.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: Specification.zip
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: xlsx
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0416_PAcs.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0601_TPEE.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0655_PE.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0675_ETFE.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0838_PA.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0842_PVC.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0933_ETFE.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0935_PP.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0973_PE.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0978_NTC.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0983_PVC.pdf
2025-04-14 15:50:42,639 [INFO] 文件标记为已删除: YR-0986_NTC.pdf
2025-04-14 15:50:42,643 [INFO] 处理文件夹: Specification
2025-04-14 15:50:42,643 [INFO] 文件夹标记为已删除: True
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0416_PAcs.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0601_TPEE.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0655_PE.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0675_ETFE.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0838_PA.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0842_PVC.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0933_ETFE.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0935_PP.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0973_PE.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0978_NTC.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0983_PVC.pdf
2025-04-14 15:50:42,644 [INFO] 文件标记为已删除: YR-0986_NTC.pdf
2025-04-14 15:50:42,648 [INFO] 数据库更新成功
2025-04-14 15:50:42,847 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:42,847 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:49,000 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:49,000 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:50:54,322 [INFO] 用户 cv24051 上传文件: YR-0416_PAcs.pdf 到文件夹: Specification
2025-04-14 15:50:54,620 [INFO] 用户 cv24051 上传文件: YR-0601_TPEE.pdf 到文件夹: Specification
2025-04-14 15:50:54,906 [INFO] 用户 cv24051 上传文件: YR-0655_PE.pdf 到文件夹: Specification
2025-04-14 15:50:55,184 [INFO] 用户 cv24051 上传文件: YR-0675_ETFE.pdf 到文件夹: Specification
2025-04-14 15:50:55,520 [INFO] 用户 cv24051 上传文件: YR-0838_PA.pdf 到文件夹: Specification
2025-04-14 15:50:55,846 [INFO] 用户 cv24051 上传文件: YR-0842_PVC.pdf 到文件夹: Specification
2025-04-14 15:50:56,157 [INFO] 用户 cv24051 上传文件: YR-0933_ETFE.pdf 到文件夹: Specification
2025-04-14 15:50:56,460 [INFO] 用户 cv24051 上传文件: YR-0935_PP.pdf 到文件夹: Specification
2025-04-14 15:50:56,798 [INFO] 用户 cv24051 上传文件: YR-0973_PE.pdf 到文件夹: Specification
2025-04-14 15:50:57,115 [INFO] 用户 cv24051 上传文件: YR-0978_NTC.pdf 到文件夹: Specification
2025-04-14 15:50:57,430 [INFO] 用户 cv24051 上传文件: YR-0983_PVC.pdf 到文件夹: Specification
2025-04-14 15:50:57,745 [INFO] 用户 cv24051 上传文件: YR-0986_NTC.pdf 到文件夹: Specification
2025-04-14 15:50:58,937 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:50:58,937 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:51:01,959 [INFO] 开始删除文件夹: Specification
2025-04-14 15:51:01,959 [INFO] 处理文件夹: Specification
2025-04-14 15:51:01,960 [INFO] 文件夹标记为已删除: True
2025-04-14 15:51:01,966 [INFO] 文件标记为已删除: YR-0416_PAcs.pdf
2025-04-14 15:51:01,966 [INFO] 文件标记为已删除: YR-0601_TPEE.pdf
2025-04-14 15:51:01,966 [INFO] 文件标记为已删除: YR-0655_PE.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0675_ETFE.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0838_PA.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0842_PVC.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0933_ETFE.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0935_PP.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0973_PE.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0978_NTC.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0983_PVC.pdf
2025-04-14 15:51:01,967 [INFO] 文件标记为已删除: YR-0986_NTC.pdf
2025-04-14 15:51:01,975 [INFO] 数据库更新成功
2025-04-14 15:51:02,171 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:51:02,171 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:52:13,211 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:52:13,211 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:52:16,027 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:52:16,027 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:53:39,169 [INFO] 生成验证码: HUVZ
2025-04-14 15:53:39,190 [INFO] 生成验证码: K68H
2025-04-14 15:55:53,153 [INFO] 验证码比对: 输入=rn8e, 存储=rn8e
2025-04-14 15:55:53,316 [INFO] 生成验证码: 398N
2025-04-14 15:55:53,383 [INFO] 生成验证码: KMSF
2025-04-14 15:56:15,748 [INFO] 验证码比对: 输入=kmsf, 存储=kmsf
2025-04-14 15:56:22,288 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 15:56:22,410 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 15:56:22,427 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:56:22,427 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:59:01,571 [INFO] 用户 cv24051 退出登录
2025-04-14 15:59:01,636 [INFO] 生成验证码: A7K2
2025-04-14 15:59:01,671 [INFO] 生成验证码: FUSN
2025-04-14 15:59:16,496 [INFO] 验证码比对: 输入=fusn, 存储=fusn
2025-04-14 15:59:16,616 [INFO] 用户 王振岗 登录成功
2025-04-14 15:59:16,627 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 15:59:16,627 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 15:59:53,423 [INFO] 用户 王振岗 退出登录
2025-04-14 15:59:53,474 [INFO] 生成验证码: K59A
2025-04-14 15:59:53,517 [INFO] 生成验证码: V9HZ
2025-04-14 16:00:07,277 [INFO] 验证码比对: 输入=v9hz, 存储=v9hz
2025-04-14 16:00:07,397 [INFO] 用户 业务 登录成功
2025-04-14 16:00:07,406 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:00:07,406 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:00:58,068 [INFO] 用户 业务 退出登录
2025-04-14 16:00:58,137 [INFO] 生成验证码: WXP7
2025-04-14 16:00:58,186 [INFO] 生成验证码: AMA5
2025-04-14 16:02:09,494 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:02:09,494 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:03:10,274 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:03:10,274 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:03:12,688 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:03:12,688 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:03:15,751 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:03:15,751 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:03:49,626 [INFO] 验证码比对: 输入=ama5, 存储=ama5
2025-04-14 16:03:49,747 [INFO] 用户 王振岗 登录成功
2025-04-14 16:03:49,756 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:03:49,756 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:03:50,022 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:03:50,023 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:04:16,116 [INFO] 用户 王振岗 退出登录
2025-04-14 16:04:16,176 [INFO] 生成验证码: 5TLY
2025-04-14 16:04:16,197 [INFO] 生成验证码: XT93
2025-04-14 16:04:34,813 [INFO] 用户 cv24051 上传文件: pdf 到文件夹: 根目录
2025-04-14 16:04:35,399 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:04:35,400 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:04:36,573 [INFO] 验证码比对: 输入=xt93, 存储=xt93
2025-04-14 16:04:36,700 [INFO] 用户 何秀珍 登录成功
2025-04-14 16:04:36,708 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:04:36,708 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:04:55,533 [INFO] 用户 何秀珍 退出登录
2025-04-14 16:04:55,597 [INFO] 生成验证码: K4WM
2025-04-14 16:04:55,632 [INFO] 生成验证码: FUK6
2025-04-14 16:05:09,136 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 16:05:09,143 [INFO] 找到 1135 条日志记录
2025-04-14 16:05:10,588 [INFO] 验证码比对: 输入=fuk6, 存储=fuk6
2025-04-14 16:05:10,708 [INFO] 用户 林志勇 登录成功
2025-04-14 16:05:10,717 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:05:10,717 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:05:12,351 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:05:12,351 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:05:48,587 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:05:48,588 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:05:49,242 [INFO] 生成验证码: 2MLH
2025-04-14 16:05:49,454 [INFO] 生成验证码: DMWU
2025-04-14 16:05:52,788 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:05:52,788 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: 111, 范围: all
2025-04-14 16:05:54,057 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:05:54,058 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:08:44,696 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:08:44,697 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:10:08,843 [INFO] 用户 林志勇 退出登录
2025-04-14 16:10:08,903 [INFO] 生成验证码: HRRU
2025-04-14 16:10:08,934 [INFO] 生成验证码: 2JMD
2025-04-14 16:10:18,290 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:10:18,290 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:11:08,033 [INFO] 验证码比对: 输入=2jmd, 存储=2jmd
2025-04-14 16:11:08,159 [INFO] 用户 林志勇 登录成功
2025-04-14 16:11:08,167 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:11:08,167 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:11:58,126 [INFO] 用户 林志勇 退出登录
2025-04-14 16:11:58,169 [INFO] 生成验证码: PRKZ
2025-04-14 16:11:58,224 [INFO] 生成验证码: TXT8
2025-04-14 16:12:11,417 [INFO] 验证码比对: 输入=txt8, 存储=txt8
2025-04-14 16:12:13,737 [INFO] 安全问题验证: 用户=cv24051
2025-04-14 16:12:13,854 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-14 16:12:13,862 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:12:13,862 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:14:02,118 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:14:02,119 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:14:12,515 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:14:12,515 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:14:49,239 [INFO] File Manager startup
2025-04-14 16:14:50,357 [INFO] File Manager startup
2025-04-14 16:15:14,928 [INFO] File Manager startup
2025-04-14 16:15:14,949 [INFO] File Manager startup
2025-04-14 16:15:15,015 [INFO] File Manager startup
2025-04-14 16:15:15,105 [INFO] File Manager startup
2025-04-14 16:15:15,166 [INFO] File Manager startup
2025-04-14 16:15:15,179 [INFO] File Manager startup
2025-04-14 16:15:15,210 [INFO] File Manager startup
2025-04-14 16:15:15,229 [INFO] File Manager startup
2025-04-14 16:15:27,651 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:15:27,651 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:15:59,084 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:15:59,084 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:00,414 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:00,415 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:03,344 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:03,344 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:04,763 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:04,763 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:09,554 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:09,554 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:10,194 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:10,195 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:39,663 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:39,664 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:16:40,928 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:16:40,929 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:17:01,768 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:17:01,769 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:23:31,233 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 16:23:31,237 [INFO] 找到 1152 条日志记录
2025-04-14 16:23:39,461 [INFO] 用户 cv24051 退出登录
2025-04-14 16:23:39,535 [INFO] 生成验证码: AW8A
2025-04-14 16:23:39,568 [INFO] 生成验证码: RBL8
2025-04-14 16:30:29,876 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 16:30:29,876 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 16:30:32,808 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 16:30:32,812 [INFO] 找到 1153 条日志记录
2025-04-14 17:00:03,809 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:03,809 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:00:06,411 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:06,411 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:00:20,983 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:20,983 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:00:37,691 [INFO] File Manager startup
2025-04-14 17:00:37,697 [INFO] File Manager startup
2025-04-14 17:00:38,199 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:38,199 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:00:56,715 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:56,715 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:00:57,518 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:00:57,518 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:03,692 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:03,693 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:05,359 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:05,359 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:07,411 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:07,412 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:09,660 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:09,661 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: 111, 范围: all
2025-04-14 17:01:11,407 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:11,407 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:13,154 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:13,154 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:14,339 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:01:14,339 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:01:44,899 [INFO] File Manager startup
2025-04-14 17:02:16,076 [INFO] File Manager startup
2025-04-14 17:02:16,109 [INFO] File Manager startup
2025-04-14 17:18:53,529 [INFO] File Manager startup
2025-04-14 17:18:53,614 [INFO] File Manager startup
2025-04-14 17:20:52,885 [INFO] File Manager startup
2025-04-14 17:20:52,972 [INFO] File Manager startup
2025-04-14 17:21:28,197 [INFO] File Manager startup
2025-04-14 17:21:28,206 [INFO] File Manager startup
2025-04-14 17:21:29,068 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:21:29,068 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:21:33,166 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:21:33,166 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:22:05,480 [INFO] File Manager startup
2025-04-14 17:22:05,483 [INFO] File Manager startup
2025-04-14 17:23:01,714 [INFO] File Manager startup
2025-04-14 17:23:01,770 [INFO] File Manager startup
2025-04-14 17:29:20,863 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:29:20,863 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:29:25,177 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:29:25,177 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:29:36,183 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:29:36,183 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:29:38,335 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 17:29:38,335 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 17:30:09,448 [INFO] File Manager startup
2025-04-14 17:30:09,517 [INFO] File Manager startup
2025-04-14 17:31:09,781 [INFO] File Manager startup
2025-04-14 17:31:09,796 [INFO] File Manager startup
2025-04-14 17:32:09,023 [INFO] File Manager startup
2025-04-14 17:32:09,093 [INFO] File Manager startup
2025-04-14 17:33:09,378 [INFO] File Manager startup
2025-04-14 17:33:09,389 [INFO] File Manager startup
2025-04-14 17:34:09,633 [INFO] File Manager startup
2025-04-14 17:34:09,650 [INFO] File Manager startup
2025-04-14 18:23:18,384 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-14 18:23:18,387 [INFO] 找到 1158 条日志记录
2025-04-14 18:23:22,778 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-14 18:23:22,778 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-14 18:24:37,502 [INFO] File Manager startup
2025-04-14 18:24:37,531 [INFO] File Manager startup
2025-04-15 09:30:10,478 [INFO] 生成验证码: CFWM
2025-04-15 09:30:10,544 [INFO] 生成验证码: C9HR
2025-04-15 09:30:20,733 [INFO] 验证码比对: 输入=c9hr, 存储=c9hr
2025-04-15 09:30:20,869 [INFO] 用户 夏欢钰 登录成功
2025-04-15 09:30:20,902 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-15 09:30:20,902 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-15 09:30:30,649 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-15 09:30:30,649 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-15 10:41:25,050 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-15 10:41:25,050 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-15 17:29:29,569 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-15 17:29:29,570 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-15 17:29:43,903 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-15 17:29:43,904 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-16 16:03:26,463 [INFO] 生成验证码: G3KK
2025-04-16 16:03:26,542 [INFO] 生成验证码: AG6H
2025-04-16 19:08:52,565 [INFO] 生成验证码: 4FC6
2025-04-16 19:08:52,592 [INFO] 生成验证码: 2VWH
2025-04-17 08:10:59,869 [INFO] File Manager startup
2025-04-17 09:12:52,451 [INFO] 生成验证码: 6SR7
2025-04-17 09:12:52,480 [INFO] 生成验证码: JPEK
2025-04-17 15:49:52,027 [INFO] File Manager startup
2025-04-17 15:55:25,947 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:55:25,947 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:55:33,037 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-17 15:55:33,041 [INFO] 找到 1161 条日志记录
2025-04-17 15:55:49,552 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:55:49,553 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:55:50,697 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:55:50,697 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:22,633 [INFO] File Manager startup
2025-04-17 15:56:31,519 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:31,520 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:36,419 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:36,419 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:41,628 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:41,628 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:45,980 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:45,980 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:47,371 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:47,371 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:53,189 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:53,189 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:56,652 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:56,652 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:56:59,029 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:56:59,029 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:57:01,796 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:57:01,796 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:57:05,757 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:57:05,757 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:57:41,876 [INFO] File Manager startup
2025-04-17 15:57:41,941 [INFO] File Manager startup
2025-04-17 15:59:12,380 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:12,380 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:59:13,431 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:13,431 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:59:14,678 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:14,678 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:59:38,517 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:38,518 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:59:55,623 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:55,623 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 15:59:57,145 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 15:59:57,145 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 16:00:01,545 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 16:00:01,545 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 16:00:03,945 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-17 16:00:03,950 [INFO] 找到 1163 条日志记录
2025-04-17 16:00:12,522 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-17 16:00:12,522 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-17 17:23:14,141 [INFO] File Manager startup
2025-04-17 17:23:14,218 [INFO] File Manager startup
2025-04-17 21:10:42,081 [INFO] 生成验证码: FQSN
2025-04-17 21:10:42,110 [INFO] 生成验证码: VGUV
2025-04-18 14:41:06,076 [INFO] File Manager startup
2025-04-18 14:41:07,128 [INFO] File Manager startup
2025-04-21 10:54:04,276 [INFO] 生成验证码: 2M7W
2025-04-21 10:54:04,329 [INFO] 生成验证码: NNKU
2025-04-21 10:54:44,185 [INFO] 验证码比对: 输入=nnku, 存储=nnku
2025-04-21 10:54:44,234 [INFO] 生成验证码: Z3A6
2025-04-21 10:54:44,257 [INFO] 生成验证码: W8PH
2025-04-21 10:54:57,250 [INFO] 验证码比对: 输入=w8ph, 存储=w8ph
2025-04-21 10:54:59,775 [INFO] 安全问题验证: 用户=cv24051
2025-04-21 10:54:59,893 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-04-21 10:54:59,906 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 10:54:59,907 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 10:55:01,893 [INFO] 请求日志页面：页码=1, 操作=, 用户=None, 搜索=
2025-04-21 10:55:01,900 [INFO] 找到 1164 条日志记录
2025-04-21 10:55:23,821 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 10:55:23,822 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:05:32,200 [INFO] 生成验证码: QANM
2025-04-21 18:05:32,245 [INFO] 生成验证码: 8X8A
2025-04-21 18:05:43,201 [INFO] 验证码比对: 输入=8x8a, 存储=8x8a
2025-04-21 18:05:43,326 [INFO] 用户 彭富强 登录成功
2025-04-21 18:05:43,343 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:05:43,344 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:22,630 [INFO] 用户 彭富强 上传文件: 2UEWH_0.06X1000_N_7TF.pdf 到文件夹: 根目录
2025-04-21 18:08:22,659 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:22,659 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:29,523 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:29,523 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:32,052 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:32,052 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:33,039 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:33,039 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:33,207 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:33,208 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:48,835 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:48,835 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:52,147 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:52,148 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:08:57,569 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:08:57,569 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:09:37,445 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:09:37,445 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:09:41,120 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:09:41,120 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:09:59,641 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:09:59,642 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:10:34,579 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:10:34,579 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:11:26,058 [INFO] 用户 彭富强 上传文件: 3UEF_0.05X2000_N_7TF.pdf 到文件夹: 根目录
2025-04-21 18:11:26,087 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:11:26,088 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:11:57,313 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:11:57,313 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:12:04,141 [INFO] 用户 彭富强 上传文件: 3UEF_0.07x1500_N_7TF.pdf 到文件夹: 根目录
2025-04-21 18:12:04,167 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:12:04,167 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:12:30,753 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:12:30,753 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:14:13,134 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:14:13,134 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:14:21,734 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:14:21,734 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:15:08,173 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:08,173 [INFO] 搜索请求 - 文件名: 漆包绞线, 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:15:56,225 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:56,225 [INFO] 搜索请求 - 文件名: 漆包绞线, 匹配方式: exact, 标签: 漆包绞线 2UEWH 0.06X1000 N 7TF, 范围: current
2025-04-21 18:15:57,592 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:57,592 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:15:57,767 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:57,767 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:15:57,951 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:57,951 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:15:59,231 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:59,231 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:15:59,360 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:15:59,361 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: all
2025-04-21 18:16:10,049 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:10,050 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:12,448 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:12,448 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:19,563 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:19,564 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:27,177 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:27,178 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:28,177 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:28,177 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:33,327 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:33,328 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:34,315 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:34,315 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-21 18:16:34,491 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-21 18:16:34,491 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-22 19:55:33,056 [INFO] 生成验证码: EFGL
2025-04-22 19:55:34,070 [INFO] 生成验证码: YVA4
2025-04-23 16:24:16,285 [INFO] 生成验证码: JPUY
2025-04-23 16:24:16,330 [INFO] 生成验证码: RLNY
2025-04-23 16:24:27,826 [INFO] 验证码比对: 输入=rlny, 存储=rlny
2025-04-23 16:24:27,951 [INFO] 用户 夏欢钰 登录成功
2025-04-23 16:24:27,962 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-23 16:24:27,962 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-23 16:24:34,827 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-23 16:24:34,827 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-04-23 16:24:44,901 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-04-23 16:24:44,902 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:16,216 [INFO] File Manager startup
2025-05-28 14:30:29,054 [INFO] 生成验证码: AXRR
2025-05-28 14:30:29,089 [INFO] 生成验证码: 5K77
2025-05-28 14:30:40,327 [INFO] 验证码比对: 输入=5k77, 存储=5k77
2025-05-28 14:30:42,546 [INFO] 安全问题验证: 用户=cv24051
2025-05-28 14:30:42,669 [INFO] 用户 cv24051 通过安全问题验证并登录成功
2025-05-28 14:30:42,689 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:42,689 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:49,097 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:49,098 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:50,863 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:50,863 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:51,694 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:51,695 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:52,667 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:52,667 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:53,208 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:53,208 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:54,306 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:54,306 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:54,977 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:54,977 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:55,641 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:55,642 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:56,266 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:56,266 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:57,138 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:57,138 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 14:30:57,684 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-28 14:30:57,684 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-28 21:26:35,309 [INFO] File Manager startup
2025-05-29 08:21:48,550 [INFO] File Manager startup
2025-05-29 08:30:42,789 [INFO] File Manager startup
2025-05-29 08:31:34,144 [INFO] File Manager startup
2025-05-29 11:03:09,501 [INFO] File Manager startup
2025-05-29 11:03:23,756 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:03:23,756 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:03:24,862 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:03:24,863 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:03:28,282 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:03:28,282 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:03:28,742 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:03:28,742 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:03:36,843 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.file_id' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:03:52,738 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:03:52,738 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:03:55,335 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.file_id' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 858}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:12:56,967 [INFO] File Manager startup
2025-05-29 11:13:04,575 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:13:05,189 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:13:05,468 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:13:06,789 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:13:06,789 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:13:11,408 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:13:22,105 [ERROR] 保存签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature_date' in 'field list'")
[SQL: INSERT INTO signature (user_id, file_id, signature_data, signature_date, signature_metadata) VALUES (%(user_id)s, %(file_id)s, %(signature_data)s, %(signature_date)s, %(signature_metadata)s)]
[parameters: {'user_id': 1, 'file_id': 859, 'signature_data': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA2kAAADICAYAAAByIKlwAAAAAXNSR0IArs4c6QAAIABJREFUeF7tnX/oV1f9x8/KmJJrCo4cc6yBgvYDHBgoOGxtAytiCo4pbXwa84+ ... (18984 characters truncated) ... lahnaBQEIQAACEIAABCAAAQj0kgAirZdmp9MQgAAEIAABCEAAAhCAQKoEEGmpWoZ2QQACEIAABCAAAQhAAAK9JIBI66XZ6TQEIAABCEAAAhCAAAQgkCqB/wMo33J60V5NZwAAAABJRU5ErkJggg==', 'signature_date': datetime.datetime(2025, 5, 29, 11, 13, 22, 103620, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'signature_metadata': '{}'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:13:37,501 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:13,175 [INFO] File Manager startup
2025-05-29 11:16:16,445 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:17,482 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:17,948 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:18,123 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:18,282 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:18,442 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:18,604 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:18,799 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:20,155 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:16:21,308 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:16:21,308 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:16:24,129 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:21:32,068 [INFO] File Manager startup
2025-05-29 11:21:35,442 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:21:36,013 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:21:36,297 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:21:37,205 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:21:37,205 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:21:42,062 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:26:06,440 [INFO] File Manager startup
2025-05-29 11:26:07,732 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:26:08,767 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:26:09,590 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:26:09,590 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:26:14,277 [ERROR] 查询签名失败，可能是数据库结构问题: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.signature_date' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata 
FROM signature 
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 11:28:20,652 [INFO] File Manager startup
2025-05-29 11:28:24,865 [INFO] File Manager startup
2025-05-29 11:28:36,782 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:28:36,782 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:28:59,477 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:28:59,477 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:29:34,119 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:29:34,119 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:29:49,928 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:29:49,928 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:30:03,888 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:30:03,888 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:30:14,023 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:30:14,023 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:30:15,240 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:30:15,240 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:30:23,975 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:30:23,976 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:30:25,347 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:30:25,347 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:37:28,658 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:37:28,658 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:37:49,985 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:37:49,985 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:37:51,502 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:37:51,503 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:38:06,072 [INFO] 关键字筛选请求 - 关键字1: , 关键字2: , 选择文件夹: 
2025-05-29 11:38:06,072 [INFO] 搜索请求 - 文件名: , 匹配方式: fuzzy, 标签: , 范围: current
2025-05-29 11:41:18,291 [INFO] File Manager startup
