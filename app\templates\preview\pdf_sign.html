{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ filename }} - 添加手写签名</h5>
                    <div class="btn-group">
                        <button id="btnSaveSignature" class="btn btn-sm btn-success">
                            <i class="fas fa-save"></i> 保存签名
                        </button>
                        <button id="btnClearSignature" class="btn btn-sm btn-warning">
                            <i class="fas fa-eraser"></i> 清除签名
                        </button>
                        <a href="{{ url_for('main.preview_file', file_id=file_id) }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <!-- 签名工具栏 -->
                    <div class="bg-light p-2 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹颜色</span>
                                    <input type="color" id="penColor" class="form-control form-control-color" value="#000000">
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹粗细</span>
                                    <input type="range" id="penSize" class="form-range" min="1" max="10" value="2" style="width: 100px;">
                                    <span class="input-group-text" id="penSizeValue">2px</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">使用保存的签名</span>
                                    <select id="selectSignature" class="form-select form-select-sm">
                                        <option value="">-- 选择签名 --</option>
                                        {% for signature in saved_signatures %}
                                        <option value="{{ signature.id }}" data-signature="{{ signature.data }}">{{ signature.date }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto">
                                <span class="text-muted small">点击PDF文档位置添加签名</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- PDF容器 -->
                    <div id="pdfContainer" class="pdf-container position-relative" style="min-height: 800px;">
                        <!-- iframe用于显示PDF文件 -->
                        <iframe id="pdfViewer" src="{{ file_url }}" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; border: none; z-index: 1;"></iframe>
                        
                        <!-- 签名层 -->
                        <div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="pointer-events: none; z-index: 2;"></div>
                        
                        <!-- 添加透明覆盖层，用于捕获点击事件 -->
                        <div id="overlayLayer" class="position-absolute top-0 start-0 w-100 h-100" style="z-index: 3; cursor: crosshair;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 签名模态框 -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-pen"></i> 添加手写签名</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3 text-center">
                    <p class="alert alert-info">请在下方框内用鼠标签署您的签名</p>
                    <div class="signature-pad-container border rounded" style="background-color: #f8f9fa; padding: 10px;">
                        <canvas id="signaturePad" width="450" height="200" style="width: 100%; height: 200px; border: 2px dashed #007bff; background-color: white; cursor: crosshair;"></canvas>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearPad">
                    <i class="fas fa-eraser"></i> 清除
                </button>
                <button type="button" class="btn btn-primary" id="confirmSignature">
                    <i class="fas fa-check"></i> 确认签名
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .signature-pad-container {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    #signaturePad {
        touch-action: none; /* 改善触摸设备上的响应 */
    }
    
    /* 为签名板添加提示文字 */
    .signature-pad-container::before {
        content: "点击并拖动鼠标完成签名";
        display: block;
        text-align: center;
        color: #0d6efd;
        margin-bottom: 5px;
        font-size: 0.9rem;
        font-weight: bold;
    }
    
    .signature-item {
        position: absolute;
        cursor: move;
        z-index: 100;
        pointer-events: auto !important;
        border: 2px dashed #ff0000;
        padding: 5px;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .signature-image {
        max-width: 200px;
        border: none;
        display: block;
    }
    
    .signature-item:hover {
        border-color: #ff0000;
        background-color: rgba(255, 255, 255, 0.5);
    }
    
    .signature-controls {
        position: absolute;
        top: -35px;
        right: 0;
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 4px;
        padding: 3px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        z-index: 101;
        display: flex;
        border: 1px solid #ccc;
    }
    
    /* 始终显示控制按钮 */
    .signature-item .signature-controls {
        display: flex !important;
    }
    
    /* 控制按钮样式 */
    .signature-controls .btn {
        margin-right: 3px;
        padding: 2px 5px;
        font-size: 12px;
    }
    
    .signature-controls .btn:last-child {
        margin-right: 0;
    }
    
    /* 确保签名层位于iframe之上 */
    #signatureLayer {
        z-index: 50;
        pointer-events: auto !important;
    }
    
    /* 确保iframe不会拦截鼠标事件 */
    #pdfViewer {
        pointer-events: none;
    }
    
    /* 透明覆盖层样式 */
    #overlayLayer {
        background-color: transparent;
        pointer-events: auto;
    }
    
    /* 确保签名模态框显示在最上层 */
    .modal {
        z-index: 9999;
    }
    
    /* 修复移动设备上的触摸事件 */
    @media (hover: none) and (pointer: coarse) {
        #signatureLayer {
            touch-action: none;
        }
        
        .signature-item {
            touch-action: none;
        }
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("PDF签名页面初始化...");
    
    // 初始化变量
    const signatureLayer = document.getElementById('signatureLayer');
    const overlayLayer = document.getElementById('overlayLayer');
    const pdfContainer = document.getElementById('pdfContainer');
    const btnSaveSignature = document.getElementById('btnSaveSignature');
    const btnClearSignature = document.getElementById('btnClearSignature');
    const penColor = document.getElementById('penColor');
    const penSize = document.getElementById('penSize');
    const penSizeValue = document.getElementById('penSizeValue');
    const selectSignature = document.getElementById('selectSignature');
    
    // 添加调试事件监听器
    pdfContainer.addEventListener('click', function(e) {
        console.log("pdfContainer 点击事件触发", e.clientX, e.clientY);
    });
    
    overlayLayer.addEventListener('click', function(e) {
        console.log("overlayLayer 点击事件触发", e.clientX, e.clientY);
    });
    
    signatureLayer.addEventListener('click', function(e) {
        console.log("signatureLayer 点击事件触发", e.clientX, e.clientY);
    });
    
    // 确保模态框元素存在
    if (!document.getElementById('signatureModal')) {
        console.error("找不到签名模态框元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    const signatureModal = new bootstrap.Modal(document.getElementById('signatureModal'));
    
    let signatureItems = [];
    let currentSignature = null;
    let isDragging = false;
    let offsetX, offsetY;
    
    // 初始化签名板
    const canvas = document.getElementById('signaturePad');
    if (!canvas) {
        console.error("找不到签名板元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    // 使用更完善的方式初始化SignaturePad
    let signaturePad;
    try {
        signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)',
            minWidth: 2,
            maxWidth: 5,
            throttle: 16, // 添加节流控制以获得更流畅的体验
            velocityFilterWeight: 0.5, // 调整笔迹平滑度
        });
        console.log("SignaturePad初始化成功");
        
        // 测试签名板是否工作
        canvas.addEventListener('mousedown', function(e) {
            console.log("签名板检测到鼠标按下事件", e);
        });
        
    } catch (e) {
        console.error("SignaturePad初始化失败:", e);
        alert("签名功能初始化失败，请刷新页面重试");
        return;
    }
    
    // 自适应调整画布大小
    function resizeCanvas() {
        // 使用固定大小而不是动态计算
        canvas.width = 450;
        canvas.height = 200;
        
        // 重新初始化背景
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = "#FFFFFF";
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        if (signaturePad) {
            signaturePad.clear(); // 清除现有签名
        }
        
        console.log("Canvas大小已调整:", canvas.width, canvas.height);
    }
    
    // 初始调整一次
    resizeCanvas();
    
    // 确认签名按钮点击事件
    const confirmBtn = document.getElementById('confirmSignature');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            console.log("确认签名按钮被点击");
            
            // 检查是否为空
            if (signaturePad.isEmpty()) {
                alert('请先签名');
                return;
            }
            
            try {
                // 获取签名数据
                const signatureData = signaturePad.toDataURL('image/png');
                console.log("获取签名数据成功，长度:", signatureData.length);
                
                // 确保有位置信息
                if (!currentSignature || typeof currentSignature.x === 'undefined') {
                    console.error("签名位置信息无效:", currentSignature);
                    // 使用默认位置
                    currentSignature = { x: 100, y: 100 };
                    console.log("使用默认位置:", currentSignature);
                }
                
                // 直接创建一个简单的签名图像并添加到文档
                const signatureDiv = document.createElement('div');
                signatureDiv.className = 'signature-item';
                signatureDiv.style.position = 'absolute';
                signatureDiv.style.left = currentSignature.x + 'px';
                signatureDiv.style.top = currentSignature.y + 'px';
                signatureDiv.style.zIndex = '100';
                signatureDiv.style.cursor = 'move';
                signatureDiv.style.pointerEvents = 'auto';
                
                const img = document.createElement('img');
                img.src = signatureData;
                img.className = 'signature-image';
                img.style.maxWidth = '200px';
                img.style.maxHeight = '100px';
                img.style.border = '1px dashed #007bff';
                img.draggable = false;
                
                signatureDiv.appendChild(img);
                
                // 添加删除按钮
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'btn btn-sm btn-danger signature-delete-btn';
                deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
                deleteBtn.style.position = 'absolute';
                deleteBtn.style.top = '-10px';
                deleteBtn.style.right = '-10px';
                deleteBtn.style.display = 'none';
                deleteBtn.addEventListener('click', function() {
                    signatureLayer.removeChild(signatureDiv);
                    console.log("签名已删除");
                });
                
                // 显示/隐藏删除按钮
                signatureDiv.addEventListener('mouseenter', function() {
                    deleteBtn.style.display = 'block';
                });
                signatureDiv.addEventListener('mouseleave', function() {
                    deleteBtn.style.display = 'none';
                });
                
                signatureDiv.appendChild(deleteBtn);
                
                // 添加拖拽功能
                signatureDiv.addEventListener('mousedown', function(e) {
                    if (e.target === deleteBtn || e.target.closest('.btn')) {
                        return;
                    }
                    
                    isDragging = true;
                    currentSignature = signatureDiv;
                    
                    const rect = signatureDiv.getBoundingClientRect();
                    offsetX = e.clientX - rect.left;
                    offsetY = e.clientY - rect.top;
                    
                    console.log("开始拖动签名元素");
                    e.preventDefault();
                });
                
                signatureLayer.appendChild(signatureDiv);
                
                // 保存签名项目数据，用于最终提交
                signatureItems.push({
                    element: signatureDiv,
                    data: signatureData,
                    x: currentSignature.x,
                    y: currentSignature.y
                });
                
                console.log("签名已添加到位置:", currentSignature.x, currentSignature.y);
                console.log("当前签名数量:", signatureItems.length);
                
                // 关闭模态框
                signatureModal.hide();
            } catch (e) {
                console.error("确认签名过程中出错:", e);
                alert("创建签名时出错: " + e.message);
            }
        });
    }
    
    // 点击PDF区域时，显示签名模态框
    if (pdfContainer) {
        console.log("为PDF容器添加点击事件");
        signatureLayer.addEventListener('click', function(e) {
            console.log("PDF容器被点击", e.target);
            
            // 忽略已有签名上的点击
            if (e.target.closest('.signature-item') || e.target.tagName === 'IMG') {
                console.log("点击了现有签名或图片，忽略");
                return;
            }
            
            // 计算点击位置
            const rect = pdfContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            console.log(`点击位置: x=${x}, y=${y}`);
            
            // 暂存位置信息 - 确保位置信息有效
            currentSignature = { x: Math.round(x), y: Math.round(y) };
            console.log("已设置当前签名位置:", currentSignature);
            
            // 显示签名板
            signatureModal.show();
        });
    } else {
        console.error("找不到PDF容器元素！");
    }
    
    // 清除签名板
    const clearPadBtn = document.getElementById('clearPad');
    if (clearPadBtn) {
        clearPadBtn.addEventListener('click', function() {
            console.log("清除签名板");
            signaturePad.clear();
        });
    }
    
    // 如果有预保存的签名，添加选择事件
    if (selectSignature) {
        selectSignature.addEventListener('change', function() {
            console.log("选择了保存的签名");
            const selectedOption = this.options[this.selectedIndex];
            if (!selectedOption.value) return;
            
            // 获取选中的签名数据
            const signatureData = selectedOption.getAttribute('data-signature');
            
            // 询问用户放置位置
            alert('请点击PDF上您希望放置签名的位置');
            
            // 监听一次性点击事件
            const clickHandler = function(e) {
                const rect = pdfContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                console.log(`选择放置位置: x=${x}, y=${y}`);
                addSignatureToDocument(signatureData, x, y);
                
                // 移除事件监听器
                pdfContainer.removeEventListener('click', clickHandler);
                
                // 重置选择框
                selectSignature.selectedIndex = 0;
            };
            
            // 添加一次性点击事件
            pdfContainer.addEventListener('click', clickHandler);
        });
    }
    
    // 添加签名到文档
    function addSignatureToDocument(signatureData, posX, posY) {
        console.log(`添加签名到文档: x=${posX}, y=${posY}`);
        try {
            // 创建签名元素
            const signatureDiv = document.createElement('div');
            signatureDiv.className = 'signature-item';
            signatureDiv.style.position = 'absolute';
            signatureDiv.style.left = posX + 'px';
            signatureDiv.style.top = posY + 'px';
            signatureDiv.style.zIndex = '100';
            
            // 创建签名图像
            const img = document.createElement('img');
            img.src = signatureData;
            img.className = 'signature-image';
            img.style.width = '150px'; // 设置初始宽度
            img.style.maxHeight = '100px';
            img.draggable = false;
            
            signatureDiv.appendChild(img);
            
            // 添加控制按钮组
            const controlsGroup = document.createElement('div');
            controlsGroup.className = 'signature-controls';
            
            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-sm btn-danger me-1';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = '删除';
            deleteBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                signatureLayer.removeChild(signatureDiv);
                // 从数组中移除
                const index = signatureItems.findIndex(item => item.element === signatureDiv);
                if (index !== -1) {
                    signatureItems.splice(index, 1);
                }
                console.log("签名已删除");
            };
            
            // 放大按钮 - 使用更明显的图标和颜色
            const enlargeBtn = document.createElement('button');
            enlargeBtn.className = 'btn btn-sm btn-primary me-1 btn-enlarge';
            enlargeBtn.innerHTML = '<i class="fas fa-plus"></i>';
            enlargeBtn.title = '放大签名';
            enlargeBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                // 使用全局函数处理放大
                window.enlargeSignature(img);
            };
            
            // 缩小按钮 - 使用更明显的图标和颜色
            const shrinkBtn = document.createElement('button');
            shrinkBtn.className = 'btn btn-sm btn-info me-1 btn-shrink';
            shrinkBtn.innerHTML = '<i class="fas fa-minus"></i>';
            shrinkBtn.title = '缩小签名';
            shrinkBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                // 使用全局函数处理缩小
                window.shrinkSignature(img);
            };

            // 修改按钮 - 点击可以重新签名
            const editBtn = document.createElement('button');
            editBtn.className = 'btn btn-sm btn-warning me-1';
            editBtn.innerHTML = '<i class="fas fa-pen"></i>';
            editBtn.title = '修改签名';
            editBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 保存当前位置信息
                currentSignature = {
                    x: parseInt(signatureDiv.style.left),
                    y: parseInt(signatureDiv.style.top),
                    element: signatureDiv,
                    index: signatureItems.findIndex(item => item.element === signatureDiv)
                };
                
                // 清除签名板并显示模态框
                if (signaturePad) {
                    signaturePad.clear();
                }
                
                // 设置模态框确认按钮的行为为更新现有签名
                const confirmBtn = document.getElementById('confirmSignature');
                const originalOnClick = confirmBtn.onclick;
                
                confirmBtn.onclick = function() {
                    if (signaturePad.isEmpty()) {
                        alert('请先签名');
                        return;
                    }
                    
                    // 获取新签名数据
                    const newSignatureData = signaturePad.toDataURL('image/png');
                    
                    // 更新图像
                    img.src = newSignatureData;
                    
                    // 更新数据数组
                    if (currentSignature.index !== -1) {
                        signatureItems[currentSignature.index].data = newSignatureData;
                    }
                    
                    // 关闭模态框
                    signatureModal.hide();
                    
                    // 恢复原始确认按钮行为
                    confirmBtn.onclick = originalOnClick;
                    
                    console.log("签名已更新");
                };
                
                // 显示模态框
                signatureModal.show();
            };
            
            // 添加按钮到控制组
            controlsGroup.appendChild(deleteBtn);
            controlsGroup.appendChild(editBtn);
            controlsGroup.appendChild(enlargeBtn);
            controlsGroup.appendChild(shrinkBtn);
            signatureDiv.appendChild(controlsGroup);
            
            // 添加拖拽功能 - 简化拖拽逻辑
            signatureDiv.onmousedown = function(e) {
                // 忽略控制按钮上的点击
                if (e.target.closest('.signature-controls') || e.target.closest('.btn')) {
                    return;
                }
                
                console.log("开始拖动签名");
                isDragging = true;
                currentSignature = signatureDiv;
                
                const rect = signatureDiv.getBoundingClientRect();
                offsetX = e.clientX - rect.left;
                offsetY = e.clientY - rect.top;
                
                // 确保签名元素在拖动时显示在最前面
                signatureDiv.style.zIndex = 1000;
                
                e.preventDefault();
            };
            
            // 添加到签名层
            signatureLayer.appendChild(signatureDiv);
            
            // 记录签名数据
            signatureItems.push({
                element: signatureDiv,
                data: signatureData,
                x: posX,
                y: posY
            });
            
            console.log("签名元素已添加到文档");
            console.log("当前签名项目数:", signatureItems.length);
        } catch (e) {
            console.error("添加签名到文档时出错:", e);
            alert("添加签名到文档时出错: " + e.message);
        }
    }
    
    // 处理拖拽
    document.addEventListener('mousemove', function(e) {
        if (!isDragging || !currentSignature) return;
        
        const rect = pdfContainer.getBoundingClientRect();
        const x = e.clientX - rect.left - offsetX;
        const y = e.clientY - rect.top - offsetY;
        
        // 限制在PDF容器内
        const maxX = rect.width - currentSignature.offsetWidth;
        const maxY = rect.height - currentSignature.offsetHeight;
        const boundedX = Math.max(0, Math.min(x, maxX));
        const boundedY = Math.max(0, Math.min(y, maxY));
        
        currentSignature.style.left = boundedX + 'px';
        currentSignature.style.top = boundedY + 'px';
        
        // 更新数组中的位置
        const index = signatureItems.findIndex(item => item.element === currentSignature);
        if (index !== -1) {
            signatureItems[index].x = boundedX;
            signatureItems[index].y = boundedY;
        }
        
        // 添加调试输出
        console.log(`拖动中: x=${boundedX}, y=${boundedY}`);
        
        // 阻止事件进一步传播
        e.preventDefault();
        e.stopPropagation();
    });
    
    document.addEventListener('mouseup', function(e) {
        if (isDragging) {
            console.log("结束拖动签名");
            // 恢复正常的z-index
            if (currentSignature) {
                currentSignature.style.zIndex = '100';
                
                // 记录最终位置
                const finalX = parseInt(currentSignature.style.left);
                const finalY = parseInt(currentSignature.style.top);
                console.log(`签名最终位置: x=${finalX}, y=${finalY}`);
                
                // 更新数组中的位置
                const index = signatureItems.findIndex(item => item.element === currentSignature);
                if (index !== -1) {
                    signatureItems[index].x = finalX;
                    signatureItems[index].y = finalY;
                }
            }
            
            // 重置拖动状态
            isDragging = false;
            currentSignature = null;
            
            // 阻止事件进一步传播
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // 笔迹粗细变化
    if (penSize && penSizeValue) {
        penSize.addEventListener('input', function() {
            const size = this.value;
            penSizeValue.textContent = size + 'px';
            signaturePad.minWidth = size / 2;
            signaturePad.maxWidth = size * 2;
        });
    }
    
    // 笔迹颜色变化
    if (penColor) {
        penColor.addEventListener('input', function() {
            signaturePad.penColor = this.value;
        });
    }
    
    // 清除所有签名
    if (btnClearSignature) {
        btnClearSignature.addEventListener('click', function() {
            console.log("清除所有签名");
            if (confirm('确定要清除所有签名吗？')) {
                while (signatureLayer.firstChild) {
                    signatureLayer.removeChild(signatureLayer.firstChild);
                }
                signatureItems = [];
            }
        });
    }
    
    // 保存签名
    if (btnSaveSignature) {
        btnSaveSignature.addEventListener('click', function() {
            console.log("保存签名按钮被点击");
            if (signatureItems.length === 0) {
                alert('请先添加至少一个签名');
                return;
            }
            
            // 显示加载提示
            const loadingToast = document.createElement('div');
            loadingToast.className = 'position-fixed top-50 start-50 translate-middle p-3 bg-dark text-white rounded';
            loadingToast.style.zIndex = 9999;
            loadingToast.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div> 正在保存签名...';
            document.body.appendChild(loadingToast);
            
            // 使用html2canvas捕获整个PDF和签名
            setTimeout(() => {
                console.log("开始捕获PDF和签名");
                html2canvas(pdfContainer, {
                    allowTaint: true,
                    useCORS: true,
                    logging: true,
                    onclone: function(clonedDoc) {
                        console.log("文档已克隆");
                    }
                }).then(canvas => {
                    console.log("PDF和签名捕获成功");
                    const screenshot = canvas.toDataURL('image/png');
                    console.log("截图数据长度:", screenshot.length);
                    
                    // 准备签名数据
                    const signatures = signatureItems.map(item => {
                        // 获取当前位置和尺寸信息
                        const element = item.element;
                        const x = parseInt(element.style.left);
                        const y = parseInt(element.style.top);
                        const img = element.querySelector('img');
                        const width = img ? (parseInt(img.style.width) || img.width) : 0;
                        
                        return {
                            data: item.data,
                            x: x,
                            y: y,
                            width: width
                        };
                    });
                    
                    console.log("准备发送签名数据到服务器");
                    console.log("签名数量:", signatures.length);
                    
                    // 发送到服务器
                    fetch('{{ url_for("main.save_pdf_signature", file_id=file_id) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            screenshot: screenshot,
                            signatures: signatures
                        })
                    })
                    .then(response => {
                        console.log("服务器响应状态:", response.status);
                        return response.json();
                    })
                    .then(data => {
                        document.body.removeChild(loadingToast);
                        console.log("服务器响应数据:", data);
                        if (data.success) {
                            alert('签名保存成功!');
                            window.location.href = '{{ url_for("main.preview_file", file_id=file_id) }}';
                        } else {
                            alert('保存失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        document.body.removeChild(loadingToast);
                        console.error('Error:', error);
                        alert('保存签名时出错: ' + error.message);
                    });
                }).catch(error => {
                    document.body.removeChild(loadingToast);
                    console.error('截图错误:', error);
                    alert('创建签名截图时出错: ' + error.message);
                });
            }, 500); // 增加延迟，确保UI渲染完成
        });
    }

    // 确保iframe内容加载完成后再绑定事件
    const pdfViewer = document.getElementById('pdfViewer');
    if (pdfViewer) {
        pdfViewer.onload = function() {
            console.log("PDF iframe已加载完成");
            // 确保签名层可交互
            signatureLayer.style.pointerEvents = 'auto';
            // 移除iframe的pointer-events，这样点击事件可以传递到签名层
            pdfViewer.style.pointerEvents = 'none';
            
            // 手动添加一个透明覆盖层来捕获点击事件
            const overlayDiv = document.createElement('div');
            overlayDiv.style.position = 'absolute';
            overlayDiv.style.top = '0';
            overlayDiv.style.left = '0';
            overlayDiv.style.width = '100%';
            overlayDiv.style.height = '100%';
            overlayDiv.style.zIndex = '10';
            overlayDiv.style.backgroundColor = 'transparent';
            overlayDiv.style.pointerEvents = 'auto';
            
            overlayDiv.addEventListener('click', function(e) {
                console.log("覆盖层被点击");
                
                // 忽略已有签名上的点击
                if (e.target.closest('.signature-item')) {
                    console.log("点击了现有签名，忽略");
                    return;
                }
                
                // 计算点击位置
                const rect = pdfContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                console.log(`点击位置: x=${x}, y=${y}`);
                
                // 暂存位置信息
                currentSignature = { x, y };
                
                // 显示签名板
                signatureModal.show();
            });
            
            signatureLayer.appendChild(overlayDiv);
            
            console.log("调试信息 - 签名层样式:", {
                pointerEvents: signatureLayer.style.pointerEvents,
                zIndex: signatureLayer.style.zIndex,
                width: signatureLayer.offsetWidth,
                height: signatureLayer.offsetHeight
            });
        };
    }

    // 处理透明覆盖层的点击事件
    overlayLayer.addEventListener('click', function(e) {
        if (selectSignature.value) {
            // 如果选择了预设签名，不处理
            return;
        }
        
        console.log("检测到覆盖层点击，打开签名模态框");
        
        // 计算点击位置
        const rect = pdfContainer.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 设置当前签名位置
        currentSignature = { x: Math.round(x), y: Math.round(y) };
        console.log(`设置签名位置: x=${currentSignature.x}, y=${currentSignature.y}`);
        
        // 清除上一次的签名
        if (signaturePad) {
            signaturePad.clear();
        }
        
        // 显示签名模态框
        signatureModal.show();
    });

    // 添加调试函数
    function debugSignatureControls() {
        console.log("===== 签名控制按钮调试 =====");
        
        // 检查所有签名项
        const signatureItems = document.querySelectorAll('.signature-item');
        console.log(`找到 ${signatureItems.length} 个签名项`);
        
        signatureItems.forEach((item, index) => {
            console.log(`签名 #${index+1}:`);
            console.log(`- 位置: left=${item.style.left}, top=${item.style.top}`);
            
            // 检查控制按钮
            const controls = item.querySelector('.signature-controls');
            if (controls) {
                const buttons = controls.querySelectorAll('button');
                console.log(`- 控制按钮: ${buttons.length} 个`);
                
                // 检查每个按钮
                buttons.forEach((btn, btnIndex) => {
                    console.log(`  - 按钮 #${btnIndex+1}: class=${btn.className}, title=${btn.title}, 可见=${btn.offsetParent !== null}`);
                });
            } else {
                console.log(`- 控制按钮: 未找到`);
            }
            
            // 检查图像
            const img = item.querySelector('img');
            if (img) {
                console.log(`- 图像: width=${img.style.width || 'auto'}, src长度=${img.src.length}`);
            }
        });
        
        console.log("===== 调试结束 =====");
    }

    // 在文档加载完成后运行调试
    const debugBtn = document.createElement('button');
    debugBtn.className = 'btn btn-sm btn-secondary ms-2';
    debugBtn.innerHTML = '<i class="fas fa-bug"></i> 调试';
    debugBtn.title = '调试签名功能';
    debugBtn.onclick = debugSignatureControls;
    
    // 添加到工具栏
    const toolbar = document.querySelector('.bg-light.p-2.border-bottom .row');
    if (toolbar) {
        const div = document.createElement('div');
        div.className = 'col-auto';
        div.appendChild(debugBtn);
        toolbar.appendChild(div);
    }
    
    // 覆盖原有的放大缩小按钮事件处理
    window.enlargeSignature = function(img) {
        const currentWidth = parseInt(img.style.width) || 150;
        const newWidth = Math.round(currentWidth * 1.2);
        img.style.width = newWidth + 'px';
        console.log("签名已放大到", newWidth + 'px');
    };
    
    window.shrinkSignature = function(img) {
        const currentWidth = parseInt(img.style.width) || 150;
        const newWidth = Math.max(50, Math.round(currentWidth * 0.8));
        img.style.width = newWidth + 'px';
        console.log("签名已缩小到", newWidth + 'px');
    };

    console.log("PDF签名页面初始化完成");
});
</script>
{% endblock %} 
